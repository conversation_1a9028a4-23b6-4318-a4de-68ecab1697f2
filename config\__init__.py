# 空文件，使 config 目录成为 Python 包 


# ==========================================
# 数据库配置说明 (更新于迁移完成后)
# ==========================================
# 系统已完成从双数据库模式到单数据库模式的迁移
# 所有数据现在都存储在主数据库(aps)中
# SQLALCHEMY_BINDS配置已被注释，不再使用系统数据库
# ==========================================


import os
from datetime import timedelta

# 从环境变量加载.env文件
from dotenv import load_dotenv
load_dotenv()

# 导入外部配置读取器
try:
    from app.utils.config_reader import get_database_config, get_application_config
    _external_config_available = True
except ImportError:
    _external_config_available = False

class Config:
    # 优先从外部配置文件读取配置
    if _external_config_available:
        try:
            _external_db_config = get_database_config()
            _external_app_config = get_application_config()
        except:
            _external_db_config = {}
            _external_app_config = {}
    else:
        _external_db_config = {}
        _external_app_config = {}
    
    # Security
    SECRET_KEY = _external_app_config.get('secret_key') or os.environ.get('SECRET_KEY') or 'dev-secret-key'
    
    # 数据库类型配置 (mysql, postgresql)
    DB_TYPE = os.environ.get('DB_TYPE') or 'mysql'

    # 数据库连接参数 - 优先使用外部配置文件，exe环境严格要求配置
    if _external_db_config:
        # 使用外部配置文件
        DB_HOST = _external_db_config.get('host') or 'localhost'
        DB_USER = _external_db_config.get('user') or 'root'
        DB_PASSWORD = _external_db_config.get('password') or ''
        DB_NAME = _external_db_config.get('database') or 'aps'
        DB_CHARSET = _external_db_config.get('charset') or 'utf8mb4'
        DB_PORT = _external_db_config.get('port') or 3306
    else:
        # 检查是否为exe环境
        import sys
        if getattr(sys, 'frozen', False):
            raise Exception("❌ exe环境必须提供config.ini配置文件！")
        # 开发环境回退到环境变量和默认值
        DB_HOST = os.environ.get('DB_HOST') or os.environ.get('MYSQL_HOST') or 'localhost'
        DB_USER = os.environ.get('DB_USER') or 'root'
        DB_PASSWORD = os.environ.get('DB_PASSWORD') or 'WWWwww123!'
        DB_NAME = os.environ.get('DB_NAME') or 'aps'
        DB_CHARSET = os.environ.get('DB_CHARSET') or 'utf8mb4'
        DB_PORT = int(os.environ.get('DB_PORT', 3306))
    
    DB_SYSTEM_NAME = os.environ.get('DB_SYSTEM_NAME') or 'aps'

    # 根据DB_TYPE动态生成URI
    if DB_TYPE == 'mysql':
        SQLALCHEMY_DATABASE_URI = f"mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}?charset={DB_CHARSET}"
        # SQLALCHEMY_BINDS = {  # 已迁移到单数据库模式
        #     'system': f"mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_SYSTEM_NAME}?charset={DB_CHARSET}"  # 已迁移到单数据库模式
        # }
    elif DB_TYPE == 'postgresql':
        if not hasattr(Config, 'DB_PORT'):
            DB_PORT = int(os.environ.get('DB_PORT') or 5432)
        SQLALCHEMY_DATABASE_URI = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
        # SQLALCHEMY_BINDS = {  # 已迁移到单数据库模式
        #     'system': f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_SYSTEM_NAME}"  # 已迁移到单数据库模式
        # }
    else:
        raise ValueError(f"Unsupported DB_TYPE: {DB_TYPE}")

    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # Session security
    PERMANENT_SESSION_LIFETIME = timedelta(hours=8)  # 会话最长持续8小时
    SESSION_COOKIE_SECURE = False  # 在生产环境中应设置为True，要求HTTPS
    SESSION_COOKIE_HTTPONLY = True  # 防止JavaScript访问cookie
    SESSION_COOKIE_SAMESITE = 'Lax'  # 防止CSRF攻击
    
    # 请求处理
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 限制上传文件大小为16MB
    
    # 并发控制
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_size': 10,  # 数据库连接池大小
        'max_overflow': 20,  # 最大额外连接数
        'pool_timeout': 30,  # 连接池超时时间
        'pool_recycle': 3600,  # 自动回收连接的时间
    }
    
    # 日志配置
    LOG_LEVEL = os.environ.get('LOG_LEVEL') or 'INFO'
    LOG_FILE = os.environ.get('LOG_FILE') or 'app.log'
    
    # Application specific
    THEME_COLOR = '#b81717'     # theme color
    BACKGROUND_COLOR = '#FFFFFF'  # White
    SECONDARY_BACKGROUND = '#F5F5F5'  # Light Gray
    TEXT_COLOR = '#333333'  # Dark Gray
    
    # 应用版本号
    APP_VERSION = '1.3.4'  # 当前应用版本 
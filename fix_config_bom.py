#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复config.ini文件的BOM字符问题
"""

import os
import sys

def fix_config_bom():
    """修复config.ini文件的BOM字符问题"""
    config_files = [
        'config.ini',
        'dist/config.ini'
    ]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"正在修复 {config_file}...")
            
            # 读取文件内容，去除BOM
            with open(config_file, 'rb') as f:
                content = f.read()
            
            # 检查并移除UTF-8 BOM
            if content.startswith(b'\xef\xbb\xbf'):
                print(f"  - 检测到BOM字符，正在移除...")
                content = content[3:]
            
            # 重新写入文件（无BOM）
            with open(config_file, 'wb') as f:
                f.write(content)
            
            print(f"  - ✅ {config_file} 修复完成")
        else:
            print(f"  - ⚠️ {config_file} 不存在")

def create_correct_config():
    """创建正确格式的config.ini文件"""
    config_content = """# =================================================================
# APS生产环境配置文件  
# 当前配置: 远程测试服务器
# =================================================================

[DATABASE]
# 数据库主机地址
host = *************

# 数据库端口
port = 3306

# 数据库用户名
user = root

# 数据库密码
password = WWWwww123!

# 数据库名称
database = aps

# 字符集
charset = utf8mb4

[APPLICATION]
# 应用监听地址
host = 0.0.0.0

# 应用端口
port = 5000

# 调试模式（生产环境建议设为False）
debug = False

# Excel数据路径
excel_path = ./Excel数据2025.7.23

# 应用密钥
secret_key = aps-intelligent-commander-platform-2025

[SYSTEM]
# 时区设置
timezone = Asia/Shanghai

# 日志级别
log_level = INFO

# 最大工作线程数
max_workers = 10

[LOGGING]
# 日志配置
level = INFO
file = logs/app.log
max_size = 10MB
backup_count = 5
"""
    
    # 创建dist目录（如果不存在）
    dist_dir = 'dist'
    if not os.path.exists(dist_dir):
        os.makedirs(dist_dir)
        print(f"创建目录: {dist_dir}")
    
    # 写入正确的配置文件到dist目录
    config_file = os.path.join(dist_dir, 'config.ini')
    with open(config_file, 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print(f"✅ 创建正确格式的配置文件: {config_file}")

if __name__ == "__main__":
    print("🔧 开始修复config.ini文件...")
    
    # 修复现有文件的BOM问题
    fix_config_bom()
    
    # 创建正确格式的配置文件
    create_correct_config()
    
    print("\n✅ 配置文件修复完成！")
    print("\n现在可以重新启动应用程序测试。") 
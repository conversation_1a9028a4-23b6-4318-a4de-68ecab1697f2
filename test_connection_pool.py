#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库连接池测试和监控脚本
验证连接池性能和缓存效果
"""

import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

def test_connection_pool_performance():
    """测试连接池性能"""
    print("🔄 测试数据库连接池性能...")
    
    from app.utils.db_connection_pool import get_db_cursor, get_connection_pool
    
    # 测试多次连接获取
    connection_times = []
    query_times = []
    
    for i in range(20):
        # 测试连接获取时间
        start_time = time.time()
        try:
            with get_db_cursor() as cursor:
                connect_time = time.time() - start_time
                connection_times.append(connect_time)
                
                # 测试查询时间
                query_start = time.time()
                cursor.execute("SELECT COUNT(*) as count FROM users")
                result = cursor.fetchone()
                query_time = time.time() - query_start
                query_times.append(query_time)
                
                print(f"连接 {i+1:2d}: 获取 {connect_time*1000:.1f}ms, 查询 {query_time*1000:.1f}ms, 用户数 {result['count']}")
                
        except Exception as e:
            print(f"连接 {i+1:2d}: 失败 - {e}")
    
    # 打印统计信息
    if connection_times:
        avg_connect = sum(connection_times) / len(connection_times) * 1000
        avg_query = sum(query_times) / len(query_times) * 1000
        print(f"\n📊 平均性能:")
        print(f"  连接获取: {avg_connect:.1f}ms")
        print(f"  查询执行: {avg_query:.1f}ms")
    
    # 打印连接池统计
    pool = get_connection_pool()
    pool.print_stats()

def test_concurrent_connections():
    """测试并发连接"""
    print("\n🔄 测试并发连接性能...")
    
    def worker(worker_id):
        """工作线程"""
        from app.utils.db_connection_pool import get_db_cursor
        
        results = []
        for i in range(5):
            try:
                start_time = time.time()
                with get_db_cursor() as cursor:
                    cursor.execute("SELECT SLEEP(0.1), CONNECTION_ID() as conn_id")
                    result = cursor.fetchone()
                    elapsed = time.time() - start_time
                    results.append((worker_id, i+1, elapsed, result['conn_id']))
            except Exception as e:
                results.append((worker_id, i+1, -1, f"Error: {e}"))
        
        return results
    
    # 使用线程池执行并发测试
    with ThreadPoolExecutor(max_workers=5) as executor:
        futures = [executor.submit(worker, i) for i in range(5)]
        
        all_results = []
        for future in as_completed(futures):
            all_results.extend(future.result())
    
    # 分析结果
    successful_results = [r for r in all_results if r[2] > 0]
    if successful_results:
        connection_ids = set(r[3] for r in successful_results)
        avg_time = sum(r[2] for r in successful_results) / len(successful_results)
        
        print(f"📊 并发测试结果:")
        print(f"  成功连接: {len(successful_results)}/25")
        print(f"  使用的连接ID数: {len(connection_ids)}")
        print(f"  平均响应时间: {avg_time*1000:.1f}ms")
    
    # 打印详细结果
    print(f"\n📋 详细结果:")
    for worker_id, task_id, elapsed, conn_id in all_results:
        if elapsed > 0:
            print(f"  Worker {worker_id}-{task_id}: {elapsed*1000:.1f}ms (连接ID: {conn_id})")
        else:
            print(f"  Worker {worker_id}-{task_id}: {conn_id}")

def test_config_cache():
    """测试配置缓存效果"""
    print("\n🔄 测试配置缓存效果...")
    
    from app.utils.unified_db_config import get_database_config
    
    config_times = []
    
    for i in range(10):
        start_time = time.time()
        config = get_database_config()
        elapsed = time.time() - start_time
        config_times.append(elapsed)
        
        print(f"配置读取 {i+1:2d}: {elapsed*1000:.2f}ms - {config['host']}:{config['port']}")
    
    if config_times:
        avg_time = sum(config_times) / len(config_times) * 1000
        first_time = config_times[0] * 1000
        later_avg = sum(config_times[1:]) / len(config_times[1:]) * 1000
        
        print(f"\n📊 配置缓存效果:")
        print(f"  首次读取: {first_time:.2f}ms")
        print(f"  后续平均: {later_avg:.2f}ms")
        print(f"  缓存加速: {first_time/later_avg:.1f}x")

def test_old_vs_new_performance():
    """对比旧方法和新方法的性能"""
    print("\n🔄 对比旧方法vs新方法性能...")
    
    # 测试旧方法（直接创建连接）
    print("测试旧方法（直接创建连接）:")
    old_times = []
    
    for i in range(10):
        start_time = time.time()
        try:
            from app.utils.config_reader import get_database_config
            import pymysql
            
            config = get_database_config()
            conn = pymysql.connect(
                host=config['host'],
                port=config['port'],
                user=config['user'],
                password=config['password'],
                database=config['database'],
                charset=config['charset']
            )
            
            with conn.cursor() as cursor:
                cursor.execute("SELECT 1")
                cursor.fetchone()
            
            conn.close()
            elapsed = time.time() - start_time
            old_times.append(elapsed)
            print(f"  旧方法 {i+1:2d}: {elapsed*1000:.1f}ms")
            
        except Exception as e:
            print(f"  旧方法 {i+1:2d}: 失败 - {e}")
    
    # 测试新方法（连接池）
    print("\n测试新方法（连接池）:")
    new_times = []
    
    for i in range(10):
        start_time = time.time()
        try:
            from app.utils.db_connection_pool import get_db_cursor
            
            with get_db_cursor() as cursor:
                cursor.execute("SELECT 1")
                cursor.fetchone()
            
            elapsed = time.time() - start_time
            new_times.append(elapsed)
            print(f"  新方法 {i+1:2d}: {elapsed*1000:.1f}ms")
            
        except Exception as e:
            print(f"  新方法 {i+1:2d}: 失败 - {e}")
    
    # 对比结果
    if old_times and new_times:
        old_avg = sum(old_times) / len(old_times) * 1000
        new_avg = sum(new_times) / len(new_times) * 1000
        improvement = old_avg / new_avg
        
        print(f"\n📊 性能对比:")
        print(f"  旧方法平均: {old_avg:.1f}ms")
        print(f"  新方法平均: {new_avg:.1f}ms")
        print(f"  性能提升: {improvement:.1f}x")

def monitor_connections():
    """持续监控连接池状态"""
    print("\n🔄 开始监控连接池状态（按Ctrl+C停止）...")
    
    from app.utils.db_connection_pool import get_connection_pool
    
    pool = get_connection_pool()
    
    try:
        while True:
            print(f"\n⏰ {time.strftime('%H:%M:%S')} - 连接池状态:")
            pool.print_stats()
            time.sleep(10)
    except KeyboardInterrupt:
        print("\n🛑 监控已停止")

def main():
    """主测试函数"""
    print("🚀 APS平台 - 数据库连接池性能测试")
    print("=" * 60)
    
    try:
        # 基础性能测试
        test_connection_pool_performance()
        
        # 配置缓存测试
        test_config_cache()
        
        # 并发连接测试
        test_concurrent_connections()
        
        # 性能对比测试
        test_old_vs_new_performance()
        
        print("\n✅ 所有测试完成")
        
        # 询问是否进行持续监控
        try:
            response = input("\n是否启动连接池监控? (y/N): ").lower().strip()
            if response in ['y', 'yes']:
                monitor_connections()
        except KeyboardInterrupt:
            pass
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理连接池
        try:
            from app.utils.db_connection_pool import get_connection_pool
            pool = get_connection_pool()
            pool.close_all_pools()
            print("\n🧹 连接池已清理")
        except:
            pass

if __name__ == "__main__":
    main() 
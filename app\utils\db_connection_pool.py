#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
APS平台 - 统一数据库连接池管理器
解决过度创建连接和频繁读取配置的问题
"""

import pymysql
import logging
import threading
import time
from typing import Dict, Any, Optional
from contextlib import contextmanager
from queue import Queue, Empty
from app.utils.unified_db_config import get_unified_db_config

logger = logging.getLogger(__name__)

class DatabaseConnectionPool:
    """数据库连接池管理器 - 单例模式"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        self._config = None
        self._config_loaded_time = 0
        self._config_ttl = 300  # 配置缓存5分钟
        
        # 连接池配置
        self._pool_size = 10
        self._max_connections = 20
        self._connection_timeout = 30
        
        # 连接池字典：{database_name: Queue}
        self._pools: Dict[str, Queue] = {}
        self._pool_locks: Dict[str, threading.Lock] = {}
        self._connection_counts: Dict[str, int] = {}
        
        # 统计信息
        self._stats = {
            'connections_created': 0,
            'connections_reused': 0,
            'config_cache_hits': 0,
            'config_cache_misses': 0
        }
        
        logger.info("🔄 数据库连接池管理器已初始化")
    
    def _get_cached_config(self) -> Dict[str, Any]:
        """获取缓存的数据库配置"""
        current_time = time.time()
        
        if (self._config is None or 
            current_time - self._config_loaded_time > self._config_ttl):
            
            # 配置过期或首次加载，重新读取
            try:
                self._config = get_unified_db_config().get_database_config()
                self._config_loaded_time = current_time
                self._stats['config_cache_misses'] += 1
                logger.debug("🔄 重新加载数据库配置")
            except Exception as e:
                logger.error(f"❌ 配置加载失败: {e}")
                raise
        else:
            self._stats['config_cache_hits'] += 1
        
        return self._config
    
    def _create_connection(self, database: str = 'aps') -> pymysql.Connection:
        """创建新的数据库连接"""
        config = self._get_cached_config()
        
        conn_params = {
            'host': config['host'],
            'port': config['port'],
            'user': config['user'],
            'password': config['password'],
            'database': database,
            'charset': config['charset'],
            'autocommit': False,
            'connect_timeout': self._connection_timeout,
            'cursorclass': pymysql.cursors.DictCursor
        }
        
        try:
            connection = pymysql.connect(**conn_params)
            self._stats['connections_created'] += 1
            logger.debug(f"✅ 新建数据库连接: {config['host']}:{config['port']}/{database}")
            return connection
        except Exception as e:
            logger.error(f"❌ 数据库连接创建失败: {e}")
            raise
    
    def _get_pool(self, database: str) -> Queue:
        """获取指定数据库的连接池"""
        if database not in self._pools:
            with self._lock:
                if database not in self._pools:
                    self._pools[database] = Queue(maxsize=self._max_connections)
                    self._pool_locks[database] = threading.Lock()
                    self._connection_counts[database] = 0
                    logger.info(f"🔄 为数据库 '{database}' 创建连接池")
        
        return self._pools[database]
    
    def get_connection(self, database: str = 'aps') -> pymysql.Connection:
        """从连接池获取数据库连接"""
        pool = self._get_pool(database)
        
        # 尝试从池中获取连接
        try:
            connection = pool.get_nowait()
            
            # 检查连接是否还有效
            try:
                connection.ping(reconnect=False)
                self._stats['connections_reused'] += 1
                logger.debug(f"♻️ 复用数据库连接: {database}")
                return connection
            except:
                # 连接已失效，创建新连接
                logger.debug(f"🔄 连接失效，创建新连接: {database}")
                
        except Empty:
            # 池中没有可用连接
            pass
        
        # 检查连接数限制
        with self._pool_locks[database]:
            if self._connection_counts[database] >= self._max_connections:
                # 等待可用连接
                try:
                    connection = pool.get(timeout=5)
                    connection.ping(reconnect=False)
                    self._stats['connections_reused'] += 1
                    return connection
                except (Empty, Exception):
                    raise Exception(f"数据库连接池已满，无法获取新连接: {database}")
            
            # 创建新连接
            connection = self._create_connection(database)
            self._connection_counts[database] += 1
            return connection
    
    def return_connection(self, connection: pymysql.Connection, database: str = 'aps'):
        """将连接归还到连接池"""
        if connection is None:
            return
        
        try:
            # 检查连接是否还有效
            connection.ping(reconnect=False)
            connection.rollback()  # 确保事务清洁
            
            pool = self._get_pool(database)
            
            try:
                pool.put_nowait(connection)
                logger.debug(f"🔄 连接已归还到池: {database}")
            except:
                # 池已满，关闭连接
                connection.close()
                with self._pool_locks[database]:
                    self._connection_counts[database] -= 1
                logger.debug(f"🗑️ 连接池已满，关闭连接: {database}")
                
        except Exception as e:
            # 连接有问题，直接关闭
            try:
                connection.close()
            except:
                pass
            
            with self._pool_locks[database]:
                if self._connection_counts[database] > 0:
                    self._connection_counts[database] -= 1
            
            logger.debug(f"🗑️ 问题连接已关闭: {database}")
    
    @contextmanager
    def get_cursor(self, database: str = 'aps', autocommit: bool = False):
        """获取数据库游标的上下文管理器"""
        connection = None
        cursor = None
        
        try:
            connection = self.get_connection(database)
            if autocommit:
                connection.autocommit(True)
            
            cursor = connection.cursor()
            yield cursor
            
            if not autocommit:
                connection.commit()
                
        except Exception as e:
            if connection and not autocommit:
                try:
                    connection.rollback()
                except:
                    pass
            logger.error(f"❌ 数据库操作失败: {e}")
            raise
            
        finally:
            if cursor:
                try:
                    cursor.close()
                except:
                    pass
            
            if connection:
                try:
                    if autocommit:
                        connection.autocommit(False)  # 恢复默认设置
                    self.return_connection(connection, database)
                except:
                    # 如果归还失败，强制关闭
                    try:
                        connection.close()
                    except:
                        pass
    
    @contextmanager 
    def get_connection_context(self, database: str = 'aps'):
        """获取数据库连接的上下文管理器"""
        connection = None
        
        try:
            connection = self.get_connection(database)
            yield connection
        finally:
            if connection:
                self.return_connection(connection, database)
    
    def close_all_pools(self):
        """关闭所有连接池"""
        logger.info("🔄 关闭所有数据库连接池...")
        
        for database, pool in self._pools.items():
            while not pool.empty():
                try:
                    connection = pool.get_nowait()
                    connection.close()
                except:
                    pass
            
            self._connection_counts[database] = 0
        
        self._pools.clear()
        self._pool_locks.clear()
        self._connection_counts.clear()
        
        logger.info("✅ 所有数据库连接池已关闭")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取连接池统计信息"""
        stats = self._stats.copy()
        stats['pools'] = {}
        
        for database in self._pools:
            stats['pools'][database] = {
                'active_connections': self._connection_counts.get(database, 0),
                'pool_size': self._pools[database].qsize(),
                'max_connections': self._max_connections
            }
        
        return stats
    
    def print_stats(self):
        """打印连接池统计信息"""
        stats = self.get_stats()
        
        print("📊 数据库连接池统计:")
        print(f"  新建连接: {stats['connections_created']}")
        print(f"  复用连接: {stats['connections_reused']}")
        print(f"  配置缓存命中: {stats['config_cache_hits']}")
        print(f"  配置缓存未命中: {stats['config_cache_misses']}")
        
        if stats['pools']:
            print("  连接池状态:")
            for db, pool_stats in stats['pools'].items():
                print(f"    {db}: 活跃{pool_stats['active_connections']}/池内{pool_stats['pool_size']}/最大{pool_stats['max_connections']}")


# 全局连接池实例
_connection_pool = None
_pool_lock = threading.Lock()

def get_connection_pool() -> DatabaseConnectionPool:
    """获取全局连接池实例（单例）"""
    global _connection_pool
    if _connection_pool is None:
        with _pool_lock:
            if _connection_pool is None:
                _connection_pool = DatabaseConnectionPool()
    return _connection_pool

# 便捷方法
def get_db_connection(database: str = 'aps') -> pymysql.Connection:
    """获取数据库连接（便捷方法）"""
    return get_connection_pool().get_connection(database)

def get_db_cursor(database: str = 'aps', autocommit: bool = False):
    """获取数据库游标上下文管理器（便捷方法）"""
    return get_connection_pool().get_cursor(database, autocommit)

def get_db_connection_context(database: str = 'aps'):
    """获取数据库连接上下文管理器（便捷方法）"""
    return get_connection_pool().get_connection_context(database)

def return_db_connection(connection: pymysql.Connection, database: str = 'aps'):
    """归还数据库连接（便捷方法）"""
    return get_connection_pool().return_connection(connection, database)

def print_connection_stats():
    """打印连接池统计信息（便捷方法）"""
    return get_connection_pool().print_stats() 
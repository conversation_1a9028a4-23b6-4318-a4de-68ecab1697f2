from app.utils.db_connection_pool import get_db_connection_context, get_db_connection
# -*- coding: utf-8 -*-
"""
WIP批次管理 API v2
专门处理WIP批次数据的CRUD操作

"""

from flask import Blueprint, request, jsonify
from app.utils.db_helper import get_mysql_connection
from app.utils.api_config import get_api_route
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

# 创建蓝图 - 使用配置化的URL前缀
wip_lot_api_bp = Blueprint('wip_lot_api', __name__, url_prefix=get_api_route('tables/wip_lot'))

@wip_lot_api_bp.route('/data', methods=['GET'])
def get_wip_lot_data():
    """获取WIP批次数据"""
    try:
        # 获取查询参数
        database = request.args.get('database', 'aps')
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)
        search = request.args.get('search', '')
        
        # 确保连接到aps主业务数据库
        conn = get_db_connection()  # 原参数: database='aps'
        cursor = conn.cursor()
        
        try:
            # 构建查询SQL
            base_sql = """
            SELECT 
                id, LOT_ID, LOT_TYPE, DET_LOT_TYPE, LOT_QTY, SUB_QTY,
                WIP_STATE, PROC_STATE, HOLD_STATE, DEVICE, CHIP_ID, PKG_PN,
                STAGE, GOOD_QTY, NG_QTY,
                PLAN_DUE_DATE, created_at, updated_at
            FROM wip_lot
            """
            
            # 添加搜索条件
            where_conditions = []
            params = []
            
            if search:
                where_conditions.append(
                    "(LOT_ID LIKE %s OR DEVICE LIKE %s OR STAGE LIKE %s OR WIP_STATE LIKE %s)"
                )
                search_param = f"%{search}%"
                params.extend([search_param, search_param, search_param, search_param])
            
            if where_conditions:
                base_sql += " WHERE " + " AND ".join(where_conditions)
            
            # 添加排序和分页
            base_sql += " ORDER BY created_at DESC"
            
            # 计算偏移量
            offset = (page - 1) * per_page
            base_sql += f" LIMIT {per_page} OFFSET {offset}"
            
            # 执行查询
            cursor.execute(base_sql, params)
            records = cursor.fetchall()
            
            # 获取总记录数
            count_sql = "SELECT COUNT(*) as total FROM wip_lot"
            if where_conditions:
                count_sql += " WHERE " + " AND ".join(where_conditions)
            
            cursor.execute(count_sql, params)
            total = cursor.fetchone()['total']
            
            # 格式化数据
            data = []
            for record in records:
                # 处理日期时间字段
                if record.get('PLAN_DUE_DATE'):
                    record['PLAN_DUE_DATE'] = record['PLAN_DUE_DATE'].isoformat() if hasattr(record['PLAN_DUE_DATE'], 'isoformat') else str(record['PLAN_DUE_DATE'])
                if record.get('created_at'):
                    record['created_at'] = record['created_at'].isoformat() if hasattr(record['created_at'], 'isoformat') else str(record['created_at'])
                if record.get('updated_at'):
                    record['updated_at'] = record['updated_at'].isoformat() if hasattr(record['updated_at'], 'isoformat') else str(record['updated_at'])
                
                data.append(record)
            
            result = {
                'success': True,
                'data': data,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': total,
                    'pages': (total + per_page - 1) // per_page
                },
                'message': f"获取到 {len(data)} 条WIP批次记录"
            }
            
            logger.info(f"✅ 获取WIP批次数据成功: {len(data)}条记录")
            return jsonify(result)
            
        finally:
            cursor.close()
    except Exception as e:
        logger.error(f"❌ 获取WIP批次数据失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'data': []
        }), 500

@wip_lot_api_bp.route('/summary', methods=['GET'])
def get_wip_lot_summary():
    """获取WIP批次汇总信息"""
    try:
        # 确保连接到aps主业务数据库
        conn = get_db_connection()  # 原参数: database='aps'
        cursor = conn.cursor()
        
        try:
            # 获取总记录数
            cursor.execute("SELECT COUNT(*) as total FROM wip_lot")
            total_result = cursor.fetchone()
            total_lots = total_result['total']
            
            # 获取状态分布
            cursor.execute("""
                SELECT WIP_STATE, COUNT(*) as count 
                FROM wip_lot 
                WHERE WIP_STATE IS NOT NULL 
                GROUP BY WIP_STATE 
                ORDER BY count DESC
            """)
            status_distribution = cursor.fetchall()
            
            # 获取产品分布
            cursor.execute("""
                SELECT DEVICE, COUNT(*) as count 
                FROM wip_lot 
                WHERE DEVICE IS NOT NULL 
                GROUP BY DEVICE 
                ORDER BY count DESC 
                LIMIT 10
            """)
            device_distribution = cursor.fetchall()
            
            # 获取工序分布
            cursor.execute("""
                SELECT STAGE, COUNT(*) as count 
                FROM wip_lot 
                WHERE STAGE IS NOT NULL 
                GROUP BY STAGE 
                ORDER BY count DESC
            """)
            stage_distribution = cursor.fetchall()
            
            summary = {
                'total_lots': total_lots,
                'status_distribution': status_distribution,
                'device_distribution': device_distribution,
                'stage_distribution': stage_distribution,
                'last_updated': datetime.now().isoformat()
            }
            
            logger.info(f"✅ 获取WIP批次汇总信息成功: {total_lots}条记录")
            return jsonify({
                'success': True,
                'data': summary
            })
            
        finally:
            cursor.close()
    except Exception as e:
        logger.error(f"❌ 获取WIP批次汇总信息失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@wip_lot_api_bp.route('/create', methods=['POST'])
def create_wip_lot():
    """创建WIP批次记录"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '缺少数据'
            }), 400
        
        # 检查必需字段
        if 'LOT_ID' not in data:
            return jsonify({
                'success': False,
                'error': '缺少必需字段: LOT_ID'
            }), 400
        
        # 确保连接到aps主业务数据库
        conn = get_db_connection()  # 原参数: database='aps'
        cursor = conn.cursor()
        
        try:
            # 构建插入SQL
            fields = list(data.keys())
            placeholders = ', '.join(['%s'] * len(fields))
            field_names = ', '.join([f"`{field}`" for field in fields])
            
            insert_sql = f"INSERT INTO wip_lot ({field_names}) VALUES ({placeholders})"
            values = list(data.values())
            
            cursor.execute(insert_sql, values)
            conn.commit()
            
            new_id = cursor.lastrowid
            
            logger.info(f"✅ 创建WIP批次记录成功: ID={new_id}, LOT_ID={data.get('LOT_ID')}")
            return jsonify({
                'success': True,
                'message': '创建WIP批次记录成功',
                'record_id': new_id
            })
            
        finally:
            cursor.close()
    except Exception as e:
        logger.error(f"❌ 创建WIP批次记录失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@wip_lot_api_bp.route('/update/<int:record_id>', methods=['PUT'])
def update_wip_lot(record_id):
    """更新WIP批次记录"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '缺少数据'
            }), 400
        
        # 确保连接到aps主业务数据库
        conn = get_db_connection()  # 原参数: database='aps'
        cursor = conn.cursor()
        
        try:
            # 构建更新SQL
            set_clauses = []
            values = []
            
            for field, value in data.items():
                if field != 'id':  # 不更新主键
                    set_clauses.append(f"`{field}` = %s")
                    values.append(value)
            
            if not set_clauses:
                return jsonify({
                    'success': False,
                    'error': '没有可更新的字段'
                }), 400
            
            # 添加更新时间
            set_clauses.append("`updated_at` = NOW()")
            
            update_sql = f"UPDATE wip_lot SET {', '.join(set_clauses)} WHERE id = %s"
            values.append(record_id)
            
            cursor.execute(update_sql, values)
            conn.commit()
            
            if cursor.rowcount == 0:
                return jsonify({
                    'success': False,
                    'error': '记录不存在'
                }), 404
            
            logger.info(f"✅ 更新WIP批次记录成功: ID={record_id}")
            return jsonify({
                'success': True,
                'message': '更新WIP批次记录成功'
            })
            
        finally:
            cursor.close()
    except Exception as e:
        logger.error(f"❌ 更新WIP批次记录失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@wip_lot_api_bp.route('/delete/<int:record_id>', methods=['DELETE'])
def delete_wip_lot(record_id):
    """删除WIP批次记录"""
    try:
        # 确保连接到aps主业务数据库
        conn = get_db_connection()  # 原参数: database='aps'
        cursor = conn.cursor()
        
        try:
            # 先检查记录是否存在
            cursor.execute("SELECT LOT_ID FROM wip_lot WHERE id = %s", (record_id,))
            record = cursor.fetchone()
            
            if not record:
                return jsonify({
                    'success': False,
                    'error': '记录不存在'
                }), 404
            
            # 删除记录
            cursor.execute("DELETE FROM wip_lot WHERE id = %s", (record_id,))
            conn.commit()
            
            logger.info(f"✅ 删除WIP批次记录成功: ID={record_id}, LOT_ID={record['LOT_ID']}")
            return jsonify({
                'success': True,
                'message': '删除WIP批次记录成功'
            })
            
        finally:
            cursor.close()
    except Exception as e:
        logger.error(f"❌ 删除WIP批次记录失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500 
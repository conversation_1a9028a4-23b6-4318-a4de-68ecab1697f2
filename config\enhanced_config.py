#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
APS 增强配置管理器
统一管理所有硬编码配置，支持环境变量覆盖
"""

import os
import sys
import secrets
from pathlib import Path
from typing import Optional, Dict, Any

class ConfigValidator:
    """配置验证器"""
    
    @staticmethod
    def validate_db_config(config: 'BaseConfig') -> None:
        """验证数据库配置"""
        if not config.DB_PASSWORD:
            raise ValueError("数据库密码未设置，请设置环境变量 DB_PASSWORD 或在 .env 文件中配置")
        
        if config.DB_PORT < 1 or config.DB_PORT > 65535:
            raise ValueError(f"无效的数据库端口: {config.DB_PORT}")
    
    @staticmethod
    def validate_security_config(config: 'BaseConfig') -> None:
        """验证安全配置"""
        if not config.SECRET_KEY or config.SECRET_KEY == 'dev-secret-key':
            if config.FLASK_ENV == 'production':
                raise ValueError("生产环境必须设置强密钥，请设置环境变量 SECRET_KEY")
            else:
                print("⚠️ 警告: 使用默认开发密钥，生产环境请设置 SECRET_KEY")
        
        if len(config.SECRET_KEY) < 16:
            raise ValueError("SECRET_KEY 长度至少为16个字符")
    
    @staticmethod 
    def validate_paths(config: 'BaseConfig') -> None:
        """验证路径配置"""
        try:
            # 确保必要的目录存在
            for dir_path in [config.LOG_DIR, config.UPLOAD_DIR, config.DOWNLOAD_DIR]:
                Path(dir_path).mkdir(parents=True, exist_ok=True)
        except Exception as e:
            raise ValueError(f"无法创建必要目录: {e}")
    
    @staticmethod
    def validate_api_config(config: 'BaseConfig') -> None:
        """验证API配置"""
        # 验证API端口范围
        if not (1024 <= config.API_PORT <= 65535):
            raise ValueError(f"API端口必须在1024-65535范围内，当前值: {config.API_PORT}")
        
        # 验证API超时时间
        if config.API_TIMEOUT <= 0:
            raise ValueError(f"API超时时间必须大于0秒，当前值: {config.API_TIMEOUT}")
        
        # 验证API内容长度限制
        if config.API_MAX_CONTENT_LENGTH <= 0:
            raise ValueError(f"API最大内容长度必须大于0，当前值: {config.API_MAX_CONTENT_LENGTH}")
        
        # 验证API版本格式
        if not config.API_VERSION.startswith('v') or not config.API_VERSION[1:].isdigit():
            raise ValueError(f"API版本格式错误，应为v[数字]格式，当前值: {config.API_VERSION}")
        
        # 验证Token过期时间
        if config.API_TOKEN_EXPIRE_HOURS <= 0:
            raise ValueError(f"API Token过期时间必须大于0小时，当前值: {config.API_TOKEN_EXPIRE_HOURS}")
        
        # 验证刷新Token过期时间  
        if config.API_REFRESH_TOKEN_EXPIRE_DAYS <= 0:
            raise ValueError(f"API刷新Token过期时间必须大于0天，当前值: {config.API_REFRESH_TOKEN_EXPIRE_DAYS}")
        
        # 验证CORS来源配置
        if config.API_CORS_ENABLED and not config.API_CORS_ORIGINS:
            raise ValueError("启用CORS时必须配置允许的来源")
        
        # 验证HTTP方法
        valid_methods = {'GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH', 'HEAD'}
        invalid_methods = set(config.API_CORS_METHODS) - valid_methods
        if invalid_methods:
            raise ValueError(f"无效的HTTP方法: {invalid_methods}")

class BaseConfig:
    """基础配置类"""
    
    # 数据库配置
    DB_HOST = os.environ.get('DB_HOST', 'localhost')
    DB_PORT = int(os.environ.get('DB_PORT', 3306))
    DB_USER = os.environ.get('DB_USER', 'root')
    DB_PASSWORD = os.environ.get('DB_PASSWORD')
    DB_NAME = os.environ.get('DB_NAME', 'aps')
    DB_CHARSET = os.environ.get('DB_CHARSET', 'utf8mb4')
    
    # MySQL特定配置 (向后兼容)
    @property
    def MYSQL_HOST(self) -> str:
        return os.environ.get('MYSQL_HOST', self.DB_HOST)
    
    @property
    def MYSQL_PORT(self) -> int:
        return int(os.environ.get('MYSQL_PORT', self.DB_PORT))
    
    @property
    def MYSQL_USER(self) -> str:
        return os.environ.get('MYSQL_USER', self.DB_USER)
    
    @property
    def MYSQL_PASSWORD(self) -> str:
        return os.environ.get('MYSQL_PASSWORD', self.DB_PASSWORD or 'WWWwww123!')
    
    @property
    def MYSQL_CHARSET(self) -> str:
        return os.environ.get('MYSQL_CHARSET', self.DB_CHARSET)
    
    # Redis配置
    REDIS_HOST = os.environ.get('REDIS_HOST', 'localhost')
    REDIS_PORT = int(os.environ.get('REDIS_PORT', 6379))
    REDIS_DB = int(os.environ.get('REDIS_DB', 0))
    REDIS_PASSWORD = os.environ.get('REDIS_PASSWORD', '')
    
    # Flask配置
    FLASK_HOST = os.environ.get('FLASK_HOST', '127.0.0.1')  # 本地开发使用127.0.0.1，Docker部署时设置环境变量为0.0.0.0
    FLASK_PORT = int(os.environ.get('FLASK_PORT', 5000))
    FLASK_ENV = os.environ.get('FLASK_ENV', 'development')
    SECRET_KEY = os.environ.get('SECRET_KEY', 'dev-secret-key')
    
    # 系统配置
    TIMEZONE = os.environ.get('TIMEZONE', 'Asia/Shanghai')
    DEFAULT_PAGE_SIZE = int(os.environ.get('DEFAULT_PAGE_SIZE', 1000))
    DEFAULT_UPH = int(os.environ.get('DEFAULT_UPH', 1000))
    MAX_WORKERS = int(os.environ.get('MAX_WORKERS', 10))
    
    # 路径配置
    BASE_DIR = Path(__file__).parent.parent
    EXCEL_BASE_PATH = os.environ.get('EXCEL_BASE_PATH', 'Excellist2025.06.05')
    SQLITE_DB_PATH = os.environ.get('SQLITE_DB_PATH', 'instance/aps.db')
    LOG_DIR = os.environ.get('LOG_DIR', 'logs')
    UPLOAD_DIR = os.environ.get('UPLOAD_DIR', 'uploads')
    DOWNLOAD_DIR = os.environ.get('DOWNLOAD_DIR', 'downloads')
    INSTANCE_DIR = os.environ.get('INSTANCE_DIR', 'instance')
    STATIC_EXPORTS_DIR = os.environ.get('STATIC_EXPORTS_DIR', 'static/exports')
    
    # 管理员配置
    ADMIN_USERNAME = os.environ.get('ADMIN_USERNAME', 'admin')
    ADMIN_PASSWORD = os.environ.get('ADMIN_PASSWORD', 'admin123')
    
    # 业务常量
    LARGE_BATCH_THRESHOLD = int(os.environ.get('LARGE_BATCH_THRESHOLD', 10000))
    MEDIUM_BATCH_THRESHOLD = int(os.environ.get('MEDIUM_BATCH_THRESHOLD', 1000))
    BASE_VALUE_RATE = float(os.environ.get('BASE_VALUE_RATE', 10000.0))
    MAX_PRIORITY_SCORE = float(os.environ.get('MAX_PRIORITY_SCORE', 100.0))
    MAX_PROCESSING_TIME_HOURS = int(os.environ.get('MAX_PROCESSING_TIME_HOURS', 24))
    
    # API配置
    API_PREFIX = os.environ.get('API_PREFIX', '/api')
    API_VERSION = os.environ.get('API_VERSION', 'v2')
    API_HOST = os.environ.get('API_HOST', '0.0.0.0')
    API_PORT = int(os.environ.get('API_PORT', 5000))
    API_DOCS_ENABLED = os.environ.get('API_DOCS_ENABLED', 'True').lower() == 'true'
    API_TIMEOUT = int(os.environ.get('API_TIMEOUT', 30))
    API_MAX_CONTENT_LENGTH = int(os.environ.get('API_MAX_CONTENT_LENGTH', 16777216))  # 16MB
    
    # API CORS配置
    API_CORS_ENABLED = os.environ.get('API_CORS_ENABLED', 'True').lower() == 'true'
    API_CORS_ORIGINS = os.environ.get('API_CORS_ORIGINS', '*').split(',')
    API_CORS_METHODS = os.environ.get('API_CORS_METHODS', 'GET,POST,PUT,DELETE,OPTIONS').split(',')
    
    # API认证配置
    API_AUTH_ENABLED = os.environ.get('API_AUTH_ENABLED', 'True').lower() == 'true'
    API_TOKEN_EXPIRE_HOURS = int(os.environ.get('API_TOKEN_EXPIRE_HOURS', 24))
    API_REFRESH_TOKEN_EXPIRE_DAYS = int(os.environ.get('API_REFRESH_TOKEN_EXPIRE_DAYS', 30))
    
    # API限流配置
    API_RATE_LIMIT_ENABLED = os.environ.get('API_RATE_LIMIT_ENABLED', 'True').lower() == 'true'
    API_RATE_LIMIT_DEFAULT = os.environ.get('API_RATE_LIMIT_DEFAULT', '100 per hour')
    API_RATE_LIMIT_STORAGE_URL = os.environ.get('API_RATE_LIMIT_STORAGE_URL', '')
    
    # 调试配置
    DEBUG = os.environ.get('DEBUG', 'True').lower() == 'true'
    TESTING = os.environ.get('TESTING', 'False').lower() == 'true'
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')
    QUIET_STARTUP = os.environ.get('QUIET_STARTUP', '0') == '1'
    
    # 安全配置
    CORS_ENABLED = os.environ.get('CORS_ENABLED', 'True').lower() == 'true'
    CSRF_ENABLED = os.environ.get('CSRF_ENABLED', 'True').lower() == 'true'
    SESSION_TIMEOUT = int(os.environ.get('SESSION_TIMEOUT', 3600))
    
    # 性能配置
    CACHE_ENABLED = os.environ.get('CACHE_ENABLED', 'True').lower() == 'true'
    CACHE_TIMEOUT = int(os.environ.get('CACHE_TIMEOUT', 300))
    DB_POOL_SIZE = int(os.environ.get('DB_POOL_SIZE', 10))
    DB_POOL_TIMEOUT = int(os.environ.get('DB_POOL_TIMEOUT', 30))
    
    @property
    def DATABASE_URI(self) -> str:
        """构建数据库连接URI"""
        if not self.DB_PASSWORD:
            return None
        return f"mysql+pymysql://{self.DB_USER}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}?charset={self.DB_CHARSET}"
    
    @property
    def REDIS_URL(self) -> str:
        """构建Redis连接URL"""
        if self.REDIS_PASSWORD:
            return f"redis://:{self.REDIS_PASSWORD}@{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"
        return f"redis://{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"
    
    @property
    def API_BASE_URL(self) -> str:
        """构建API基础URL"""
        protocol = "https" if self.FLASK_ENV == "production" else "http"
        return f"{protocol}://{self.API_HOST}:{self.API_PORT}{self.API_PREFIX}/{self.API_VERSION}"
    
    @property
    def API_V2_PREFIX(self) -> str:
        """API v2前缀路径"""
        return f"{self.API_PREFIX}/{self.API_VERSION}"
    
    @property
    def API_V1_PREFIX(self) -> str:
        """API v1前缀路径（向后兼容）"""
        return f"{self.API_PREFIX}/v1"
    
    @property
    def EXCEL_FULL_PATH(self) -> Path:
        """Excel文件完整路径"""
        return self.BASE_DIR / self.EXCEL_BASE_PATH
    
    @property
    def SQLITE_FULL_PATH(self) -> Path:
        """SQLite数据库完整路径"""
        return self.BASE_DIR / self.SQLITE_DB_PATH
    
    @property
    def LOG_FULL_PATH(self) -> Path:
        """日志目录完整路径"""
        return self.BASE_DIR / self.LOG_DIR
    
    def get_log_file_path(self, filename: str = 'app.log') -> Path:
        """获取日志文件路径"""
        log_dir = self.LOG_FULL_PATH
        log_dir.mkdir(parents=True, exist_ok=True)
        return log_dir / filename
    
    def validate_all(self) -> None:
        """验证所有配置"""
        ConfigValidator.validate_db_config(self)
        ConfigValidator.validate_security_config(self)
        ConfigValidator.validate_paths(self)
        ConfigValidator.validate_api_config(self)
    
    @classmethod
    def from_env_file(cls, env_path: Optional[str] = None):
        """从.env文件加载配置"""
        if env_path is None:
            env_path = cls().BASE_DIR / '.env'
        
        if Path(env_path).exists():
            with open(env_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ.setdefault(key.strip(), value.strip())
        
        return cls()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            key: getattr(self, key)
            for key in dir(self)
            if not key.startswith('_') and not callable(getattr(self, key))
        }

class DevelopmentConfig(BaseConfig):
    """开发环境配置"""
    DEBUG = True
    TESTING = False
    LOG_LEVEL = 'DEBUG'
    
    def __init__(self):
        super().__init__()
        # 开发环境特殊处理
        if self.SECRET_KEY == 'dev-secret-key':
            print("🔧 开发环境使用默认密钥，生产环境请更换")

class ProductionConfig(BaseConfig):
    """生产环境配置"""
    DEBUG = False
    TESTING = False
    LOG_LEVEL = 'WARNING'
    
    def __init__(self):
        super().__init__()
        # 生产环境强制验证
        if self.SECRET_KEY == 'dev-secret-key':
            self.SECRET_KEY = secrets.token_hex(32)
            print("🔐 生产环境自动生成安全密钥")

class TestingConfig(BaseConfig):
    """测试环境配置"""
    TESTING = True
    DEBUG = True
    DB_NAME = os.environ.get('TEST_DB_NAME', 'aps_test')
    LOG_LEVEL = 'DEBUG'

class ConfigManager:
    """配置管理器"""
    
    _configs = {
        'development': DevelopmentConfig,
        'production': ProductionConfig,
        'testing': TestingConfig,
        'default': DevelopmentConfig
    }
    
    @classmethod
    def get_config(cls, config_name: Optional[str] = None) -> BaseConfig:
        """获取配置实例"""
        if config_name is None:
            config_name = os.environ.get('FLASK_ENV', 'development')
        
        config_class = cls._configs.get(config_name, cls._configs['default'])
        
        # 尝试从.env文件加载
        try:
            return config_class.from_env_file()
        except Exception as e:
            print(f"⚠️ 从.env文件加载配置失败: {e}")
            return config_class()
    
    @classmethod
    def validate_config(cls, config: Optional[BaseConfig] = None) -> bool:
        """验证配置"""
        if config is None:
            config = cls.get_config()
        
        try:
            config.validate_all()
            print("✅ 配置验证通过")
            return True
        except Exception as e:
            print(f"❌ 配置验证失败: {e}")
            return False
    
    @classmethod
    def generate_env_template(cls, output_path: str = '.env.template') -> None:
        """生成环境变量模板文件"""
        template_content = '''# APS 车规芯片终测智能调度平台 - 环境变量配置模板
# 复制此文件为 .env 并填写实际配置值

# =================================================================
# 数据库配置 (MySQL)
# =================================================================
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_mysql_password_here
DB_NAME=aps
DB_CHARSET=utf8mb4

# =================================================================
# Redis配置
# =================================================================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=
REDIS_URL=redis://localhost:6379/0

# =================================================================
# Flask应用配置
# =================================================================
FLASK_HOST=127.0.0.1
FLASK_PORT=5000
FLASK_ENV=development
SECRET_KEY=your_secret_key_here_please_generate_random_32_chars

# =================================================================
# 系统配置
# =================================================================
TIMEZONE=Asia/Shanghai
DEFAULT_PAGE_SIZE=1000
DEFAULT_UPH=1000
MAX_WORKERS=10

# =================================================================
# 文件路径配置
# =================================================================
EXCEL_BASE_PATH=Excellist2025.06.05
SQLITE_DB_PATH=instance/aps.db
LOG_DIR=logs
UPLOAD_DIR=uploads
DOWNLOAD_DIR=downloads

# =================================================================
# 管理员配置
# =================================================================
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your_admin_password_here

# =================================================================
# 业务常量配置
# =================================================================
LARGE_BATCH_THRESHOLD=10000
MEDIUM_BATCH_THRESHOLD=1000
BASE_VALUE_RATE=10000.0
MAX_PRIORITY_SCORE=100.0
MAX_PROCESSING_TIME_HOURS=24

# =================================================================
# 调试和开发配置
# =================================================================
DEBUG=True
TESTING=False
LOG_LEVEL=INFO
QUIET_STARTUP=0

# =================================================================
# 安全配置
# =================================================================
CORS_ENABLED=True
CSRF_ENABLED=True
SESSION_TIMEOUT=3600

# =================================================================
# 性能配置
# =================================================================
CACHE_ENABLED=True
CACHE_TIMEOUT=300
DB_POOL_SIZE=10
DB_POOL_TIMEOUT=30

# =================================================================
# 配置说明:
# 1. 请将 your_mysql_password_here 替换为实际的MySQL密码
# 2. 请将 your_secret_key_here_please_generate_random_32_chars 替换为32位随机字符串
# 3. 请将 your_admin_password_here 替换为管理员密码
# 4. 生产环境请设置 FLASK_ENV=production 和 DEBUG=False
# 5. 所有路径配置支持相对路径和绝对路径
# ================================================================='''
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(template_content)
        print(f"✅ 环境变量模板已生成: {output_path}")
    
    @classmethod
    def setup_environment(cls, generate_secrets: bool = True) -> None:
        """设置环境（生成.env文件）"""
        env_file = Path('.env')
        
        if env_file.exists():
            print("⚠️ .env 文件已存在，跳过生成")
            return
        
        # 生成模板
        cls.generate_env_template()
        
        if generate_secrets:
            # 读取模板并替换占位符
            template_path = Path('.env.template')
            if template_path.exists():
                content = template_path.read_text(encoding='utf-8')
                
                # 生成随机值
                secret_key = secrets.token_hex(32)
                admin_password = secrets.token_urlsafe(16)
                
                # 替换占位符
                content = content.replace('your_secret_key_here_please_generate_random_32_chars', secret_key)
                content = content.replace('your_admin_password_here', admin_password)
                
                # 保存到.env文件
                env_file.write_text(content, encoding='utf-8')
                
                print("✅ 已自动生成 .env 文件")
                print(f"🔑 生成的密钥: {secret_key}")
                print(f"👤 管理员密码: {admin_password}")
                print("⚠️ 请手动设置数据库密码！")

# 全局配置实例
config = ConfigManager.get_config()

# 向后兼容的配置访问方式
def get_config(config_name: Optional[str] = None) -> BaseConfig:
    """获取配置实例（向后兼容）"""
    return ConfigManager.get_config(config_name)

if __name__ == '__main__':
    """命令行工具"""
    import argparse
    
    parser = argparse.ArgumentParser(description='APS配置管理工具')
    parser.add_argument('action', choices=['validate', 'generate', 'setup'], 
                       help='操作类型')
    parser.add_argument('--env', default=None, help='环境名称')
    parser.add_argument('--no-secrets', action='store_true', help='不生成密钥')
    
    args = parser.parse_args()
    
    if args.action == 'validate':
        config = ConfigManager.get_config(args.env)
        if ConfigManager.validate_config(config):
            sys.exit(0)
        else:
            sys.exit(1)
    
    elif args.action == 'generate':
        ConfigManager.generate_env_template()
    
    elif args.action == 'setup':
        ConfigManager.setup_environment(not args.no_secrets) 
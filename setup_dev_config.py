#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
开发环境配置管理脚本
快速切换不同的数据库配置
"""

import os
import sys
from typing import Dict

class DevConfigManager:
    """开发环境配置管理器"""
    
    def __init__(self):
        self.config_file = 'config.ini'
        self.example_file = 'config.ini.example'
        
    def get_predefined_configs(self) -> Dict[str, Dict]:
        """获取预定义的数据库配置"""
        return {
            'local': {
                'name': '本地MySQL',
                'host': 'localhost',
                'port': 3306,
                'user': 'root',
                'password': 'WWWwww123!',
                'database': 'aps'
            },
            'remote': {
                'name': '远程测试服务器',
                'host': '*************',
                'port': 3306,
                'user': 'root',
                'password': 'WWWwww123!',
                'database': 'aps'
            },
            'docker': {
                'name': 'Docker容器',
                'host': 'host.docker.internal',
                'port': 3306,
                'user': 'root',
                'password': 'WWWwww123!',
                'database': 'aps'
            }
        }
    
    def create_config_file(self, db_config: Dict, config_key: str = None):
        """创建配置文件"""
        config_content = f"""# =================================================================
# APS开发环境配置文件
# 当前配置: {db_config.get('name', '自定义配置')}
# =================================================================

[DATABASE]
# 数据库主机地址
host = {db_config['host']}

# 数据库端口
port = {db_config['port']}

# 数据库用户名
user = {db_config['user']}

# 数据库密码
password = {db_config['password']}

# 数据库名称
database = {db_config['database']}

# 字符集
charset = utf8mb4

[APPLICATION]
# 应用监听地址
host = 0.0.0.0

# 应用端口
port = 5000

# 调试模式
debug = True

# Excel数据路径（开发环境）
excel_path = ./Excel数据2025.7.23

[SYSTEM]
# 时区设置
timezone = Asia/Shanghai

# 日志级别
log_level = DEBUG

# 最大工作线程数
max_workers = 10
"""
        
        with open(self.config_file, 'w', encoding='utf-8') as f:
            f.write(config_content)
        
        print(f"✅ 配置文件已创建: {self.config_file}")
        if config_key:
            print(f"📋 当前配置: {config_key} - {db_config['name']}")
        print(f"🔗 数据库连接: {db_config['host']}:{db_config['port']}/{db_config['database']}")
    
    def show_current_config(self):
        """显示当前配置"""
        if not os.path.exists(self.config_file):
            print("❌ 配置文件不存在，将使用系统默认配置")
            print("💡 运行 'python setup_dev_config.py local' 创建本地配置")
            return
        
        try:
            import configparser
            config = configparser.ConfigParser()
            config.read(self.config_file, encoding='utf-8')
            
            if config.has_section('DATABASE'):
                db_config = config['DATABASE']
                print(f"📋 当前数据库配置:")
                print(f"   主机: {db_config.get('host', 'localhost')}")
                print(f"   端口: {db_config.get('port', '3306')}")
                print(f"   用户: {db_config.get('user', 'root')}")
                print(f"   数据库: {db_config.get('database', 'aps')}")
            else:
                print("⚠️ 配置文件格式不正确")
        except Exception as e:
            print(f"❌ 读取配置文件失败: {e}")
    
    def list_predefined_configs(self):
        """列出预定义配置"""
        configs = self.get_predefined_configs()
        print("🎯 可用的预定义配置:")
        for key, config in configs.items():
            print(f"   {key}: {config['name']} ({config['host']}:{config['port']})")
    
    def test_config(self, config_key: str = None):
        """测试数据库配置"""
        print("🔍 测试数据库配置...")
        
        try:
            # 测试配置读取
            if config_key:
                configs = self.get_predefined_configs()
                if config_key not in configs:
                    print(f"❌ 未知配置: {config_key}")
                    return False
                test_config = configs[config_key]
            else:
                # 测试当前配置文件
                if not os.path.exists(self.config_file):
                    print("❌ 配置文件不存在")
                    return False
                
                from app.utils.config_reader import get_database_config
                test_config = get_database_config()
            
            # 测试连接
            import pymysql
            conn = pymysql.connect(
                host=test_config['host'],
                port=test_config['port'],
                user=test_config['user'],
                password=test_config['password'],
                database=test_config['database'],
                charset='utf8mb4'
            )
            
            with conn.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
            
            conn.close()
            print(f"✅ 数据库连接测试成功!")
            print(f"🔗 连接信息: {test_config['host']}:{test_config['port']}/{test_config['database']}")
            return True
            
        except Exception as e:
            print(f"❌ 数据库连接测试失败: {e}")
            return False


def main():
    """主函数"""
    manager = DevConfigManager()
    
    if len(sys.argv) == 1:
        # 显示帮助信息
        print("🚀 APS开发环境配置管理工具")
        print("=" * 50)
        print("\n💡 使用方法:")
        print("  python setup_dev_config.py [command]")
        print("\n📋 可用命令:")
        print("  list       - 列出所有预定义配置")
        print("  current    - 显示当前配置")
        print("  local      - 切换到本地MySQL配置")
        print("  remote     - 切换到远程测试服务器")
        print("  docker     - 切换到Docker容器配置")
        print("  test       - 测试当前配置")
        print("  test <key> - 测试指定配置")
        print("\n📝 示例:")
        print("  python setup_dev_config.py local    # 切换到本地配置")
        print("  python setup_dev_config.py remote   # 切换到远程配置")
        print("  python setup_dev_config.py current  # 查看当前配置")
        print("  python setup_dev_config.py test     # 测试当前配置")
        
    elif sys.argv[1] == 'list':
        manager.list_predefined_configs()
        
    elif sys.argv[1] == 'current':
        manager.show_current_config()
        
    elif sys.argv[1] == 'test':
        if len(sys.argv) > 2:
            manager.test_config(sys.argv[2])
        else:
            manager.test_config()
            
    elif sys.argv[1] in manager.get_predefined_configs():
        # 切换到指定配置
        config_key = sys.argv[1]
        configs = manager.get_predefined_configs()
        selected_config = configs[config_key]
        
        print(f"🔄 切换到配置: {config_key}")
        manager.create_config_file(selected_config, config_key)
        
        # 自动测试配置
        print(f"\n🔍 测试新配置...")
        if manager.test_config(config_key):
            print(f"🎉 配置切换成功!")
        else:
            print(f"⚠️ 配置已创建，但连接测试失败，请检查数据库服务状态")
            
    else:
        print(f"❌ 未知命令: {sys.argv[1]}")
        print("💡 运行 'python setup_dev_config.py' 查看帮助")


if __name__ == "__main__":
    main() 
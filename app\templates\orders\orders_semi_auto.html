{% extends "base.html" %}

{% block title %}APS System {% endblock %}

{% block extra_css %}
<style>
    .card {
        margin-bottom: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .card-header {
        background-color: rgba(183, 36, 36, 0.05);
        border-bottom: 1px solid rgba(183, 36, 36, 0.1);
        font-weight: 600;
    }
    
    .nav-tabs .nav-link {
        color: #555;
        border: none;
        border-bottom: 3px solid transparent;
        border-radius: 0;
        padding: 10px 15px;
        font-weight: 500;
    }
    
    .nav-tabs .nav-link.active {
        color: #b72424;
        border-bottom: 3px solid #b72424;
        background-color: transparent;
    }
    
    .nav-tabs .nav-link:hover:not(.active) {
        border-bottom: 3px solid rgba(183, 36, 36, 0.3);
    }
    
    .btn-primary {
        background-color: #b72424;
        border-color: #b72424;
    }
    
    .btn-primary:hover {
        background-color: #971e1e;
        border-color: #971e1e;
    }
    
    .btn-outline-primary {
        color: #b72424;
        border-color: #b72424;
    }
    
    .btn-outline-primary:hover {
        background-color: #b72424;
        border-color: #b72424;
    }
    
    .table th {
        background-color: rgba(183, 36, 36, 0.05);
        border-top: none;
    }
    
    .spinner-border {
        width: 1rem;
        height: 1rem;
        border-width: 0.15em;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: rgba(183, 36, 36, 0.4);
        box-shadow: 0 0 0 0.25rem rgba(183, 36, 36, 0.1);
    }
    
    /* 可拖动模态框样式 */
    .modal-dialog-draggable .modal-header {
        cursor: move;
    }
    
    /* 取消固定定位，改用相对定位 */
    .modal-dialog-draggable {
        position: relative;
        margin: 1.75rem auto;
        transition: none; /* 禁用动画过渡效果，防止跳动 */
    }
    
    /* 邮箱配置模态框优化样式 */
    .modal-dialog-scrollable {
        max-height: 95vh;
    }
    
    .modal-dialog-scrollable .modal-content {
        max-height: 92vh;
    }
    
    .modal-dialog-scrollable .modal-body {
        overflow-y: auto;
        padding: 1.2rem;
        max-height: calc(90vh - 120px); /* 减去header和footer的高度 */
    }
    
    #emailConfigForm .row.g-3 {
        --bs-gutter-y: 0.6rem;
    }
    
    #emailConfigForm .form-text {
        margin-top: 0.2rem;
        font-size: 0.75rem;
    }
    
    @media (max-height: 700px) {
        .modal-dialog-scrollable .modal-content {
            max-height: 95vh;
        }
        
        #emailConfigForm .form-control {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }
        
        #emailConfigForm .form-label {
            margin-bottom: 0.25rem;
        }
        
        #emailConfigForm .row.g-3 {
            --bs-gutter-y: 0.4rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- 警告信息容器 -->
    <div id="alertContainer"></div>
    
    <div class="row">
        <div class="col-12">
            <ul class="nav nav-tabs mb-4" id="myTab" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="config-tab" data-bs-toggle="tab" data-bs-target="#config" type="button" role="tab" aria-controls="config" aria-selected="true">
                        <i class="fas fa-cog me-2"></i>配置管理
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="process-tab" data-bs-toggle="tab" data-bs-target="#process" type="button" role="tab" aria-controls="process" aria-selected="false">
                        <i class="fas fa-envelope me-2"></i>附件处理
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="order-tab" data-bs-toggle="tab" data-bs-target="#order" type="button" role="tab" aria-controls="order" aria-selected="false">
                        <i class="fas fa-list-alt me-2"></i>订单数据
                    </button>
                </li>
            </ul>
            
            <div class="tab-content" id="myTabContent">
                <!-- 配置管理标签页 -->
                <div class="tab-pane fade show active" id="config" role="tabpanel" aria-labelledby="config-tab">
                    <div class="row">
                        <!-- 邮箱配置 -->
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h5 class="mb-0">邮箱配置</h5>
                                        <button type="button" class="btn btn-sm btn-primary" id="newEmailConfigBtn">
                                            <i class="fas fa-plus me-1"></i>新增
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-hover" id="emailConfigTable">
                                            <thead>
                                                <tr>
                                                    <th>名称</th>
                                                    <th>邮箱</th>
                                                    <th>服务器</th>
                                                    <th>状态</th>
                                                    <th>定时任务</th>
                                                    <th>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <!-- 通过JavaScript动态加载 -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 附件处理标签页 -->
                <div class="tab-pane fade" id="process" role="tabpanel" aria-labelledby="process-tab">
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">邮件附件列表</h5>
                                <div>
                                    <select class="form-select form-select-sm d-inline-block me-2" style="width: auto;" id="emailConfigSelect">
                                        <option value="">所有邮箱</option>
                                        <!-- 通过JavaScript动态加载 -->
                                    </select>
                                    <select class="form-select form-select-sm d-inline-block me-2" style="width: auto;" id="processedFilter">
                                        <option value="">所有状态</option>
                                        <option value="1">已处理</option>
                                        <option value="0">未处理</option>
                                    </select>
                                    <input type="text" class="form-control form-control-sm d-inline-block me-2" style="width: auto;" id="attachmentKeywords" placeholder="附件名称关键词，如：宜欣 生产订单">
                                    <button type="button" class="btn btn-sm btn-primary" id="fetchAttachmentsBtn">
                                        <i class="fas fa-sync-alt me-1"></i>获取新附件
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- 添加批量处理功能区域 -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <!-- 删除此处的批量解析和汇总卡片 -->
                                </div>
                            </div>
                            <!-- 附件列表 -->
                            <div class="table-responsive">
                                <table class="table table-hover" id="attachmentsTable">
                                    <thead>
                                        <!-- 表头保持不变 -->
                                    </thead>
                                    <tbody id="attachmentsTableBody">
                                        <!-- 通过JavaScript动态加载 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 订单数据标签页 -->
                <div class="tab-pane fade" id="order" role="tabpanel" aria-labelledby="order-tab">
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">订单数据列表</h5>
                                <div>
                                    <button type="button" class="btn btn-sm btn-outline-primary me-2" id="refreshOrdersBtn">
                                        <i class="fas fa-sync-alt me-1"></i>刷新
                                    </button>
                                    <button type="button" class="btn btn-sm btn-primary" id="exportOrdersBtn">
                                        <i class="fas fa-file-export me-1"></i>导出Excel
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover" id="ordersTable">
                                    <thead>
                                        <tr>
                                            <th>订单号</th>
                                            <th>客户</th>
                                            <th>产品</th>
                                            <th>数量</th>
                                            <th>交付日期</th>
                                            <th>状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="ordersTableBody">
                                        <!-- 通过JavaScript动态加载 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 邮箱配置模态框 -->
<div class="modal fade" id="emailConfigModal" tabindex="-1" aria-labelledby="emailConfigModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-lg modal-dialog-draggable modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="emailConfigModalLabel">邮箱配置</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="emailConfigForm">
                    <input type="hidden" id="emailConfigId" name="id">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="configName" class="form-label">配置名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="configName" name="name" required>
                        </div>
                        <div class="col-md-6">
                            <label for="emailAddress" class="form-label">邮箱地址 <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" id="emailAddress" name="email" required>
                        </div>
                        <div class="col-md-6">
                            <label for="emailServer" class="form-label">服务器地址 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="emailServer" name="server" value="imap.qiye.163.com" required>
                        </div>
                        <div class="col-md-6">
                            <label for="emailPort" class="form-label">端口 <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="emailPort" name="port" value="993" required>
                        </div>
                        <div class="col-md-6">
                            <label for="emailPassword" class="form-label">客户端授权码 <span class="text-danger">*</span></label>
                            <input type="password" class="form-control" id="emailPassword" name="password" autocomplete="current-password" required>
                            <div class="form-text">请在邮箱设置中开启IMAP服务并获取授权码</div>
                        </div>
                        <div class="col-md-6">
                            <label for="checkInterval" class="form-label">检查频率（分钟）</label>
                            <input type="number" class="form-control" id="checkInterval" name="check_interval" value="60" min="5">
                        </div>
                        <div class="col-md-6">
                            <label for="workStartTime" class="form-label">工作开始时间</label>
                            <input type="time" class="form-control" id="workStartTime" name="work_start_time" value="08:00">
                        </div>
                        <div class="col-md-6">
                            <label for="workEndTime" class="form-label">工作结束时间</label>
                            <input type="time" class="form-control" id="workEndTime" name="work_end_time" value="18:00">
                        </div>
                        <div class="col-12">
                            <label for="senders" class="form-label">发件人过滤（分号分隔）</label>
                            <input type="text" class="form-control" id="senders" name="senders" placeholder="<EMAIL>; <EMAIL>">
                            <div class="form-text">留空表示接收所有发件人</div>
                        </div>
                        <div class="col-12">
                            <label for="subjects" class="form-label">主题关键词过滤（分号分隔）</label>
                            <input type="text" class="form-control" id="subjects" name="subjects" placeholder="订单; 报价">
                            <div class="form-text">留空表示接收所有主题邮件</div>
                        </div>
                        <div class="col-md-8">
                            <label for="downloadPath" class="form-label">附件保存路径 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="downloadPath" name="download_path" value="downloads/email_attachments" required>
                        </div>
                        <div class="col-md-4 d-flex align-items-end mb-3">
                            <div class="form-check mt-2">
                                <input class="form-check-input" type="checkbox" id="useDateFolder" name="use_date_folder" checked>
                                <label class="form-check-label" for="useDateFolder">
                                    按日期分类保存
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="fetchDays" class="form-label">抓取最近天数</label>
                            <input type="number" class="form-control" id="fetchDays" name="fetch_days" value="10" min="1">
                            <div class="form-text">设置每次抓取最近多少天的邮件</div>
                        </div>
                        <div class="col-md-6 d-flex align-items-end">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="configEnabled" name="enabled">
                                <label class="form-check-label" for="configEnabled">
                                    启用定时任务
                                </label>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="testConnectionBtn">测试连接</button>
                <button type="button" class="btn btn-success" id="saveEmailConfigBtn">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 附件处理模态框 -->
<div class="modal fade" id="processAttachmentModal" tabindex="-1" aria-labelledby="processAttachmentModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="processAttachmentModalLabel">处理附件</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="processAttachmentForm">
                    <input type="hidden" id="attachmentId" name="attachment_id">
                    <div class="mb-3">
                        <label for="mappingSelect" class="form-label">选择Excel映射配置 <span class="text-danger">*</span></label>
                        <select class="form-select" id="mappingSelect" name="mapping_id" required>
                            <option value="">请选择...</option>
                            <!-- 通过JavaScript动态加载 -->
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">附件信息</label>
                        <div class="card">
                            <div class="card-body">
                                <p class="mb-1"><strong>文件名：</strong><span id="attachmentFilename"></span></p>
                                <p class="mb-1"><strong>发件人：</strong><span id="attachmentSender"></span></p>
                                <p class="mb-0"><strong>接收时间：</strong><span id="attachmentReceiveDate"></span></p>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="processAttachmentBtn">处理附件</button>
            </div>
        </div>
    </div>
</div>

<!-- 订单详情模态框 -->
<div class="modal fade" id="orderDetailsModal" tabindex="-1" aria-labelledby="orderDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="orderDetailsModalLabel">订单详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="orderDetailsBody">
                    <!-- 订单详情将通过JavaScript动态加载 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 添加进度条覆盖层 -->
<div class="progress-overlay" id="progressOverlay" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); z-index: 9999; justify-content: center; align-items: center;">
    <div class="card" style="width: 400px; max-width: 90%;">
        <div class="card-header bg-light">
            <h5 class="mb-0" id="progressTitle">处理中...</h5>
        </div>
        <div class="card-body">
            <div class="progress mb-3">
                <div class="progress-bar progress-bar-striped progress-bar-animated" id="progressBar" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
            <p class="text-center mb-0" id="progressText">正在连接邮箱...</p>
        </div>
    </div>
</div>

<!-- 处理结果模态框 -->
<div class="modal fade" id="resultModal" tabindex="-1" aria-labelledby="resultModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="resultModalLabel">处理结果</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="result-summary mb-3">
                    <div class="row">
                        <div class="col-md-2 text-center">
                            <div class="card bg-light">
                                <div class="card-body py-2">
                                    <h3 class="mb-0 text-primary" id="totalEmails">0</h3>
                                    <small>处理邮件数</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="card bg-light">
                                <div class="card-body py-2">
                                    <h3 class="mb-0 text-success" id="downloadedFiles">0</h3>
                                    <small>成功下载</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 text-center">
                            <div class="card bg-light">
                                <div class="card-body py-2">
                                    <h3 class="mb-0 text-info" id="newDownloadedFiles">0</h3>
                                    <small>新增附件</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 text-center">
                            <div class="card bg-light">
                                <div class="card-body py-2">
                                    <h3 class="mb-0 text-warning" id="skippedFiles">0</h3>
                                    <small>已跳过</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="card bg-light">
                                <div class="card-body py-2">
                                    <h3 class="mb-0 text-danger" id="failedFiles">0</h3>
                                    <small>下载失败</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 处理详情标签页 -->
                <ul class="nav nav-tabs" id="resultTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="processedTab" data-bs-toggle="tab" data-bs-target="#processedContent" type="button" role="tab">成功下载 <span class="badge bg-success" id="processedBadge">0</span></button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="newTab" data-bs-toggle="tab" data-bs-target="#newContent" type="button" role="tab">新增附件 <span class="badge bg-info" id="newBadge">0</span></button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="skippedTab" data-bs-toggle="tab" data-bs-target="#skippedContent" type="button" role="tab">已跳过 <span class="badge bg-warning" id="skippedBadge">0</span></button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="failedTab" data-bs-toggle="tab" data-bs-target="#failedContent" type="button" role="tab">失败 <span class="badge bg-danger" id="failedBadge">0</span></button>
                    </li>
                </ul>
                <div class="tab-content" id="resultTabContent">
                    <div class="tab-pane fade show active" id="processedContent" role="tabpanel">
                        <div class="table-responsive mt-3">
                            <table class="table table-sm table-hover">
                                <thead>
                                    <tr>
                                        <th>文件名</th>
                                        <th>大小</th>
                                        <th>发件人</th>
                                        <th>接收日期</th>
                                        <th>状态</th>
                                    </tr>
                                </thead>
                                <tbody id="processedList">
                                    <!-- 将在JavaScript中填充 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="newContent" role="tabpanel">
                        <div class="table-responsive mt-3">
                            <table class="table table-sm table-hover">
                                <thead>
                                    <tr>
                                        <th>文件名</th>
                                        <th>大小</th>
                                        <th>发件人</th>
                                        <th>接收日期</th>
                                    </tr>
                                </thead>
                                <tbody id="newList">
                                    <!-- 新增附件列表，将在JavaScript中填充 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="skippedContent" role="tabpanel">
                        <div class="table-responsive mt-3">
                            <table class="table table-sm table-hover">
                                <thead>
                                    <tr>
                                        <th>文件名</th>
                                        <th>原因</th>
                                        <th>发件人</th>
                                        <th>主题</th>
                                    </tr>
                                </thead>
                                <tbody id="skippedList">
                                    <!-- 将在JavaScript中填充 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="failedContent" role="tabpanel">
                        <div class="table-responsive mt-3">
                            <table class="table table-sm table-hover">
                                <thead>
                                    <tr>
                                        <th>文件名</th>
                                        <th>错误原因</th>
                                        <th>发件人</th>
                                        <th>主题</th>
                                    </tr>
                                </thead>
                                <tbody id="failedList">
                                    <!-- 将在JavaScript中填充 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">确定</button>
            </div>
        </div>
    </div>
</div>

<!-- 批量处理结果模态框 -->
<div class="modal fade" id="batchResultModal" tabindex="-1" aria-labelledby="batchResultModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchResultModalLabel">批量处理结果</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="result-summary mb-3">
                    <div class="row">
                        <div class="col-md-3 text-center">
                            <div class="card bg-light">
                                <div class="card-body py-2">
                                    <h3 class="mb-0 text-primary" id="totalFiles">0</h3>
                                    <small>处理文件数</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="card bg-light">
                                <div class="card-body py-2">
                                    <h3 class="mb-0 text-success" id="successFiles">0</h3>
                                    <small>成功处理</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="card bg-light">
                                <div class="card-body py-2">
                                    <h3 class="mb-0 text-danger" id="errorFiles">0</h3>
                                    <small>处理失败</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="card bg-light">
                                <div class="card-body py-2">
                                    <h3 class="mb-0 text-info" id="totalRecords">0</h3>
                                    <small>数据记录数</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-success" id="summaryFileAlert">
                    <strong><i class="fas fa-check-circle me-1"></i>汇总文件已生成:</strong>
                    <span id="summaryFilePath"></span>
                    <a href="#" class="btn btn-sm btn-outline-success ms-2" id="downloadSummaryBtn">
                        <i class="fas fa-download me-1"></i>下载
                    </a>
                </div>
                
                <!-- 处理详情标签页 -->
                <ul class="nav nav-tabs" id="batchResultTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="successTab" data-bs-toggle="tab" data-bs-target="#successContent" type="button" role="tab">成功 <span class="badge bg-success" id="successBadge">0</span></button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="errorTab" data-bs-toggle="tab" data-bs-target="#errorContent" type="button" role="tab">失败 <span class="badge bg-danger" id="errorBadge">0</span></button>
                    </li>
                </ul>
                
                <div class="tab-content" id="batchResultTabContent">
                    <div class="tab-pane fade show active" id="successContent" role="tabpanel">
                        <div class="table-responsive mt-3">
                            <table class="table table-sm table-hover">
                                <thead>
                                    <tr>
                                        <th>文件名</th>
                                        <th>处理记录数</th>
                                        <th>处理时间</th>
                                    </tr>
                                </thead>
                                <tbody id="successList">
                                    <!-- 将在JavaScript中填充 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="errorContent" role="tabpanel">
                        <div class="table-responsive mt-3">
                            <table class="table table-sm table-hover">
                                <thead>
                                    <tr>
                                        <th>文件名</th>
                                        <th>错误原因</th>
                                    </tr>
                                </thead>
                                <tbody id="errorList">
                                    <!-- 将在JavaScript中填充 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">确定</button>
            </div>
        </div>
    </div>
</div>

<!-- Excel映射配置模态框 -->
<div class="modal fade" id="excelMappingModal" tabindex="-1" aria-labelledby="excelMappingModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-draggable modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="excelMappingModalLabel">Excel映射配置</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="excelMappingForm">
                    <input type="hidden" id="excelMappingId" name="id">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="mappingName" class="form-label">配置名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="mappingName" name="name" required placeholder="例如：生产订单映射">
                        </div>
                        <div class="col-md-6">
                            <label for="sheetName" class="form-label">工作表名称</label>
                            <input type="text" class="form-control" id="sheetName" name="sheet_name" placeholder="留空则使用第一个工作表">
                            <div class="form-text">Excel文件中的工作表名称，不填则使用第一个表</div>
                        </div>
                        <div class="col-md-6">
                            <label for="headerRow" class="form-label">表头行号 <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="headerRow" name="header_row" value="1" min="1" required>
                            <div class="form-text">表格中的表头所在行号，通常是第1行</div>
                        </div>
                        <div class="col-md-6">
                            <label for="startRow" class="form-label">数据起始行号 <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="startRow" name="start_row" value="2" min="1" required>
                            <div class="form-text">数据开始的行号，通常是表头的下一行（第2行）</div>
                        </div>
                        <div class="col-12">
                            <label for="keyFields" class="form-label">关键字段（逗号分隔）<span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="keyFields" name="key_fields" required placeholder="例如：订单号,产品编码">
                            <div class="form-text">用于识别重复记录的字段，多个字段请用逗号分隔</div>
                        </div>
                    </div>

                    <hr class="my-4">
                    <h6>字段映射设置</h6>
                    <p class="small text-muted">定义Excel列名与系统字段的对应关系，添加需要提取的所有字段</p>
                    
                    <div class="mb-3">
                        <button type="button" class="btn btn-sm btn-outline-primary" id="addFieldMappingBtn">
                            <i class="fas fa-plus me-1"></i>添加字段映射
                        </button>
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-sm" id="fieldMappingTable">
                            <thead class="table-light">
                                <tr>
                                    <th width="45%">Excel列名</th>
                                    <th width="45%">系统字段名</th>
                                    <th width="10%">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr id="fieldMappingTemplate" style="display: none;">
                                    <td>
                                        <input type="text" class="form-control form-control-sm excel-field" placeholder="Excel中的列名">
                                    </td>
                                    <td>
                                        <input type="text" class="form-control form-control-sm system-field" placeholder="系统中的字段名">
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-outline-danger remove-field-btn">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="saveExcelMappingBtn">保存配置</button>
            </div>
        </div>
    </div>
</div>

<!-- Excel映射配置区域 -->
<div class="card mb-3">
    <div class="card-header bg-light">
        <div class="d-flex justify-content-between align-items-center">
            <h6 class="mb-0"><i class="fas fa-file-excel me-2"></i>Excel映射配置</h6>
            <button type="button" class="btn btn-sm btn-primary" id="addMappingBtn">
                <i class="fas fa-plus me-1"></i>新增配置
            </button>
        </div>
    </div>
    <div class="card-body">
        <div class="alert alert-info small">
            <p><i class="fas fa-info-circle me-2"></i><strong>什么是Excel映射？</strong></p>
            <p>Excel映射是一种工具，帮助你将Excel文件中的数据按一定规则导入到系统中。就像一个翻译器，把Excel中的列名"翻译"成系统能理解的字段名。</p>
        </div>
        
        <div class="row g-3">
            <div class="col-md-12">
                <select class="form-select" id="excelMappingSelect">
                    <option value="">请选择Excel映射配置...</option>
                    <!-- 配置项将通过JavaScript动态填充 -->
                </select>
            </div>
            <div class="col-md-12 d-flex">
                <button type="button" class="btn btn-outline-primary me-2" id="editMappingBtn">
                    <i class="fas fa-edit me-1"></i>编辑
                </button>
                <button type="button" class="btn btn-outline-danger me-2" id="deleteMappingBtn">
                    <i class="fas fa-trash-alt me-1"></i>删除
                </button>
                <button type="button" class="btn btn-success flex-grow-1" id="batchProcessBtn">
                    <i class="fas fa-cogs me-2"></i>批量处理附件
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 批量处理结果区域 -->
<div class="card mb-3">
    <div class="card-header bg-light">
        <h6 class="mb-0"><i class="fas fa-chart-bar me-2"></i>批量处理结果</h6>
    </div>
    <div class="card-body">
        <div class="row g-2">
            <div class="col-md-3">
                <div class="card bg-light">
                    <div class="card-body text-center p-2">
                        <h3 id="totalFiles">0</h3>
                        <small class="text-muted">总文件数</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center p-2">
                        <h3 id="successFiles">0</h3>
                        <small>成功处理</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-danger text-white">
                    <div class="card-body text-center p-2">
                        <h3 id="errorFiles">0</h3>
                        <small>处理失败</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center p-2">
                        <h3 id="totalRecords">0</h3>
                        <small>总记录数</small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="alert alert-success mt-3" id="summaryFileAlert" style="display: none;">
            <h6><i class="fas fa-file-excel me-2"></i>汇总文件</h6>
            <p class="mb-2" id="summaryFilePath"></p>
            <button type="button" class="btn btn-sm btn-success" onclick="downloadSummaryFile()">
                <i class="fas fa-download me-1"></i>下载汇总文件
            </button>
        </div>
    </div>
</div>

<!-- 新增Excel映射配置模态框 -->
<div class="modal fade" id="addMappingModal" tabindex="-1" aria-labelledby="addMappingModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addMappingModalLabel">新增Excel映射配置</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <p><i class="fas fa-lightbulb me-2"></i><strong>小提示：</strong></p>
                    <p>创建映射配置前，建议先打开一个Excel样例文件，查看表头和数据格式，这样可以更准确地进行映射。</p>
                </div>
                
                <form id="mappingForm">
                    <div class="mb-3">
                        <label for="mappingName" class="form-label">配置名称 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="mappingName" required>
                        <div class="form-text">请为映射配置取一个易于识别的名称，例如"供应商A-订单映射"</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="mappingDescription" class="form-label">配置描述</label>
                        <textarea class="form-control" id="mappingDescription" rows="2"></textarea>
                        <div class="form-text">可选，描述此映射配置的用途或特点</div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="sheetName" class="form-label">工作表名称</label>
                            <input type="text" class="form-control" id="sheetName">
                            <div class="form-text">Excel文件中的工作表名称，不填则使用第一个表</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="headerRow" class="form-label">表头行号 <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="headerRow" value="1" min="1" required>
                            <div class="form-text">表头在Excel中的行号，一般为第1行</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="dataStartRow" class="form-label">数据起始行号 <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="dataStartRow" value="2" min="1" required>
                            <div class="form-text">数据开始的行号，通常是表头的下一行</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="dateFormat" class="form-label">日期格式</label>
                            <input type="text" class="form-control" id="dateFormat" value="YYYY-MM-DD">
                            <div class="form-text">Excel中的日期格式，如YYYY-MM-DD或MM/DD/YYYY</div>
                        </div>
                    </div>
                    
                    <hr>
                    <h6>字段映射</h6>
                    <p class="text-muted small">添加Excel列和系统字段之间的对应关系</p>
                    
                    <div id="mappingFieldsContainer">
                        <!-- 字段映射行将在这里添加 -->
                    </div>
                    
                    <div class="mt-3">
                        <button type="button" class="btn btn-sm btn-outline-primary" id="addFieldBtn">
                            <i class="fas fa-plus me-1"></i>添加字段映射
                        </button>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="saveMappingBtn">保存配置</button>
            </div>
        </div>
    </div>
</div>

<!-- 附件列表区域保留 -->
<div class="card mb-3">
    <div class="card-body">
        <div class="alert alert-info">
            <h5><i class="fas fa-info-circle me-2"></i>功能升级中</h5>
            <p>Excel映射配置功能正在重新开发中，将提供更强大、更易用的Excel文件处理能力。新功能即将推出，敬请期待！</p>
        </div>
    </div>
</div>

<div class="table-responsive">
    <table class="table table-hover" id="attachmentsTable">
        <thead>
            <!-- 表头保持不变 -->
        </thead>
        <tbody id="attachmentsTableBody">
            <!-- 通过JavaScript动态加载 -->
        </tbody>
    </table>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 全局变量
let emailConfigs = []; // 全局变量用于存储邮箱配置
let emailAttachments = []; // 全局变量用于存储邮件附件
let orderData = []; // 全局变量用于存储订单数据
// 移除了excelMappings变量

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 初始化全局变量
    emailConfigs = [];
    emailAttachments = [];
    orderData = [];
    
    // 初始化模态框拖动功能
    initDraggableModals();
    
    // 初始化标签页事件
    const tabEl = document.getElementById('myTab');
    const tabInstance = new bootstrap.Tab(tabEl);
    
    // 立即加载邮箱配置，不管当前是哪个标签页
    console.log('页面加载时立即初始化邮箱配置');
    initializeEmailConfigs();
    
    // 当导航到配置标签页时检查是否需要重新加载配置
    document.getElementById('config-tab').addEventListener('shown.bs.tab', function() {
        console.log('配置标签页被激活');
        // 如果数据为空，则重新加载
        if (emailConfigs.length === 0) {
            initializeEmailConfigs();
        }
    });
    
    // 当导航到订单标签页时加载订单数据
    document.getElementById('order-tab').addEventListener('shown.bs.tab', function() {
        console.log('订单标签页被激活');
        loadOrderData();
    });
    
    // 当导航到附件处理标签页时加载附件列表
    document.getElementById('process-tab').addEventListener('shown.bs.tab', function() {
        console.log('附件处理标签页被激活');
        // 如果有邮箱配置，加载附件列表
        if (emailConfigs.length > 0) {
            loadAttachments();
        }
    });
    
    // 邮箱配置选择下拉框变更事件
    document.getElementById('emailConfigSelect').addEventListener('change', function() {
        loadAttachments();
    });
    
    // 获取新附件按钮点击事件
    document.getElementById('fetchAttachmentsBtn').addEventListener('click', function() {
        fetchNewAttachments();
    });
    
    // 移除了所有Excel映射配置相关的事件监听器
    
    // 新增邮箱配置按钮
    document.getElementById('newEmailConfigBtn').addEventListener('click', function() {
        clearEmailConfigForm();
        document.getElementById('emailConfigModalLabel').textContent = '新增邮箱配置';
        
        // 使用默认值
        document.getElementById('emailServer').value = 'imap.qiye.163.com';
        document.getElementById('emailPort').value = '993';
        document.getElementById('downloadPath').value = 'downloads/email_attachments';
        document.getElementById('checkInterval').value = '60';
        document.getElementById('workStartTime').value = '08:00';
        document.getElementById('workEndTime').value = '18:00';
        document.getElementById('useDateFolder').checked = true;
        
        const modal = new bootstrap.Modal(document.getElementById('emailConfigModal'));
        modal.show();
    });
    
    // 测试邮箱连接按钮
    document.getElementById('testConnectionBtn').addEventListener('click', function() {
        testEmailConnection();
    });
    
    // 保存邮箱配置按钮
    document.getElementById('saveEmailConfigBtn').addEventListener('click', function() {
        saveEmailConfig();
    });
    
    // 导航到订单标签页时初始化订单数据
    document.getElementById('orders-tab').addEventListener('shown.bs.tab', function() {
        if (orderData.length === 0) {
            loadOrderData();
        }
    });
    
    // 导出订单数据按钮
    document.getElementById('exportOrdersBtn').addEventListener('click', function() {
        exportOrderData();
    });
    
    // 绑定订单标签页事件
    bindOrderTabEvents();
    
    // 初始化配置标签页
    initializeConfigTab();
    
    // 根据URL参数决定显示哪个标签页
    const urlParams = new URLSearchParams(window.location.search);
    const tabParam = urlParams.get('tab');
    
    if (tabParam) {
        // 根据URL参数激活对应标签页
        switch(tabParam) {
            case 'config':
                document.getElementById('config-tab').click();
                break;
            case 'process':
                document.getElementById('process-tab').click();
                break;
            case 'order':
                document.getElementById('order-tab').click();
                break;
            default:
                // 默认显示配置标签页
                document.getElementById('config-tab').click();
        }
    } else {
        // 如果URL中没有指定标签页，则默认显示配置标签页
        document.getElementById('config-tab').click();
    }
    
    // 添加调试日志，检查事件绑定
    console.log('页面初始化完成，事件监听器已绑定');
});

// 初始化邮箱配置
function initializeEmailConfigs() {
    console.log('开始获取邮箱配置...');
    
    // 显示加载中
    const tbody = document.querySelector('#emailConfigTable tbody');
    tbody.innerHTML = `
        <tr>
            <td colspan="6" class="text-center">
                <div class="spinner-border text-primary" role="status"></div>
                <p class="mt-2">正在加载邮箱配置...</p>
            </td>
        </tr>
    `;
    
    // 获取当前页面的基础URL
    const baseUrl = window.location.origin;
    
    // 添加时间戳防止缓存
    const timestamp = new Date().getTime();
    const apiUrl = `${baseUrl}/api/email_configs?_=${timestamp}`;
    
    console.log('请求API:', apiUrl);
    
    // 使用完整的URL路径
    fetch(apiUrl, {
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'Cache-Control': 'no-cache, no-store, must-revalidate'
        },
        credentials: 'same-origin',  // 确保发送cookies
        cache: 'no-store'  // 强制不使用缓存
    })
    .then(response => {
        console.log('API响应状态:', response.status, response.statusText);
        if (!response.ok) {
            throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.status === 'success') {
            console.log('成功获取邮箱配置:', data.data);
            // 重置全局变量
            emailConfigs = Array.isArray(data.data) ? data.data : [];
            console.log('更新后的emailConfigs数组:', emailConfigs);
            
            // 渲染表格
            renderEmailConfigTable();
            populateEmailConfigSelect();
            
            // 如果有配置，默认加载第一个配置的附件
            if (emailConfigs.length > 0 && document.getElementById('attachmentsTable')) {
                loadAttachments();
            } else {
                console.log('没有可用的邮箱配置或找不到附件表格元素');
            }
        } else {
            console.error('获取邮箱配置失败:', data.message);
            showAlert('error', '获取邮箱配置失败: ' + data.message);
            
            // 显示错误消息
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center text-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        获取邮箱配置失败: ${data.message || '未知错误'}
                    </td>
                </tr>
            `;
        }
    })
    .catch(error => {
        console.error('获取邮箱配置出错:', error);
        showAlert('error', '获取邮箱配置出错: ' + error.message);
        
        // 显示错误信息
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    无法加载邮箱配置: ${error.message}
                </td>
            </tr>
        `;
        
        // 添加手动刷新按钮
        const refreshBtn = document.createElement('button');
        refreshBtn.className = 'btn btn-sm btn-outline-primary mt-3';
        refreshBtn.innerHTML = '<i class="fas fa-sync-alt me-1"></i>重试';
        refreshBtn.onclick = function() {
            initializeEmailConfigs();
        };
        
        const refreshContainer = document.createElement('div');
        refreshContainer.className = 'text-center mt-2';
        refreshContainer.appendChild(refreshBtn);
        tbody.parentElement.parentElement.appendChild(refreshContainer);
    });
}

// 渲染邮箱配置表格
function renderEmailConfigTable() {
    console.log('渲染邮箱配置表格, 配置数量:', emailConfigs.length);
    
    const tbody = document.querySelector('#emailConfigTable tbody');
    tbody.innerHTML = '';
    
    if (!Array.isArray(emailConfigs) || emailConfigs.length === 0) {
        const tr = document.createElement('tr');
        tr.innerHTML = '<td colspan="6" class="text-center">暂无邮箱配置</td>';
        tbody.appendChild(tr);
        return;
    }
    
    emailConfigs.forEach(config => {
        console.log('渲染配置:', config);
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${config.name || '未命名'}</td>
            <td>${config.email || '无邮箱'}</td>
            <td>${config.server || '未知'}:${config.port || '0'}</td>
            <td><span class="badge ${config.enabled ? 'bg-success' : 'bg-secondary'}">${config.enabled ? '已启用' : '未启用'}</span></td>
            <td>
                <div id="task-status-${config.id}">
                    <span class="badge bg-secondary">
                        <i class="fas fa-spinner fa-spin"></i> 加载中
                    </span>
                </div>
            </td>
            <td>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-sm btn-outline-primary edit-email-config-btn" data-id="${config.id}" title="编辑" onclick="editEmailConfig(${config.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger delete-email-config-btn" data-id="${config.id}" title="删除" onclick="deleteEmailConfig(${config.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;
        tbody.appendChild(tr);
        
        // 异步加载任务状态
        if (config.enabled) {
            loadTaskStatus(config.id);
        } else {
            updateTaskStatusDisplay(config.id, { exists: false, message: '配置已禁用' });
        }
    });
}

// 填充邮箱配置下拉选择框
function populateEmailConfigSelect() {
    const select = document.getElementById('emailConfigSelect');
    if (!select) return;
    
    // 清空现有选项
    select.innerHTML = '<option value="">请选择邮箱配置...</option>';
    
    // 填充选项
    for (const config of emailConfigs) {
        const option = document.createElement('option');
        option.value = config.id;
        option.textContent = config.name;
        select.appendChild(option);
    }
}

// 加载任务状态
function loadTaskStatus(configId) {
    fetch(`/api/email_configs/${configId}/task_status`, {
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            updateTaskStatusDisplay(configId, data.data.task_status);
        } else {
            updateTaskStatusDisplay(configId, { exists: false, message: '查询失败' });
        }
    })
    .catch(error => {
        console.error('获取任务状态失败:', error);
        updateTaskStatusDisplay(configId, { exists: false, message: '网络错误' });
    });
}

// 更新任务状态显示
function updateTaskStatusDisplay(configId, taskStatus) {
    const statusElement = document.getElementById(`task-status-${configId}`);
    if (!statusElement) return;
    
    if (taskStatus.exists) {
        const nextRunTime = taskStatus.next_run_time ? new Date(taskStatus.next_run_time) : null;
        const nextRunText = nextRunTime ? 
            `下次执行: ${nextRunTime.toLocaleString()}` : 
            '待调度';
        
        statusElement.innerHTML = `
            <span class="badge bg-success" title="${nextRunText}">
                <i class="fas fa-clock"></i> 已启动
            </span>
            <br>
            <small class="text-muted" style="font-size: 0.7rem;">${nextRunText}</small>
        `;
    } else {
        const message = taskStatus.message || '未运行';
        statusElement.innerHTML = `
            <span class="badge bg-secondary" title="${message}">
                <i class="fas fa-times"></i> 未启动
            </span>
            <br>
            <small class="text-muted" style="font-size: 0.7rem;">${message}</small>
        `;
    }
}

// 编辑邮箱配置
function editEmailConfig(id) {
    const config = emailConfigs.find(c => c.id == id);
    if (!config) return;
    
    document.getElementById('emailConfigId').value = config.id;
    document.getElementById('configName').value = config.name;
    document.getElementById('emailServer').value = config.server;
    document.getElementById('emailPort').value = config.port;
    document.getElementById('emailAddress').value = config.email;
    // 密码不回显
    document.getElementById('emailPassword').value = '';
    document.getElementById('senders').value = config.senders || '';
    document.getElementById('subjects').value = config.subjects || '';
    document.getElementById('checkInterval').value = config.check_interval;
    document.getElementById('workStartTime').value = config.work_start_time;
    document.getElementById('workEndTime').value = config.work_end_time;
    document.getElementById('downloadPath').value = config.download_path;
    document.getElementById('useDateFolder').checked = config.use_date_folder;
    document.getElementById('fetchDays').value = config.fetch_days || 10;
    document.getElementById('configEnabled').checked = config.enabled;
    
    document.getElementById('emailConfigModalLabel').textContent = '编辑邮箱配置';
    
    const modal = new bootstrap.Modal(document.getElementById('emailConfigModal'));
    modal.show();
}

// 删除邮箱配置
function deleteEmailConfig(id) {
    console.log('执行删除邮箱配置函数，ID:', id);
    
    // 显示确认对话框
    if (!confirm('确定要删除此邮箱配置吗？此操作不可恢复！')) {
        console.log('用户取消了删除操作');
        return;
    }
    
    console.log('用户确认删除，发送删除请求...');
    
    // 禁用页面上的所有删除按钮，防止重复点击
    const deleteButtons = document.querySelectorAll('.delete-email-config-btn');
    deleteButtons.forEach(btn => {
        btn.disabled = true;
        btn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span>';
    });
    
    // 发送删除请求
    fetch(`/api/email_configs/${id}`, {
        method: 'DELETE',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        console.log('收到删除响应，状态码:', response.status);
        
        if (!response.ok) {
            return response.text().then(text => {
                throw new Error(`删除失败，服务器返回: ${response.status} ${text || response.statusText}`);
            });
        }
        return response.json();
    })
    .then(data => {
        console.log('删除成功，响应数据:', data);
        
        if (data.status === 'success') {
            showAlert('success', '邮箱配置删除成功');
            
            // 重新加载邮箱配置
            initializeEmailConfigs();
        } else {
            throw new Error(data.message || '删除邮箱配置失败');
        }
    })
    .catch(error => {
        console.error('删除邮箱配置出错:', error);
        showAlert('error', '删除邮箱配置失败: ' + error.message);
        
        // 恢复按钮状态
        deleteButtons.forEach(btn => {
            btn.disabled = false;
            btn.innerHTML = '<i class="fas fa-trash"></i>';
        });
    });
}

// 保存邮箱配置
function saveEmailConfig() {
    console.log('saveEmailConfig 函数被调用');
    
    const form = document.getElementById('emailConfigForm');
    
    // 检查必填字段
    const requiredFields = ['name', 'email', 'server', 'port', 'password', 'download_path'];
    let missingFields = [];
    
    for (const field of requiredFields) {
        const element = form.elements[field];
        if (!element || !element.value) {
            missingFields.push(element?.labels?.[0]?.textContent.replace(' *', '') || field);
        }
    }
    
    if (missingFields.length > 0) {
        console.log('缺少必填字段:', missingFields);
        showAlert('warning', '请填写必填字段: ' + missingFields.join(', '));
        form.reportValidity();
        return;
    }
    
    // 添加表单验证调试
    console.log('表单验证状态:', form.checkValidity());
    
    if (!form.checkValidity()) {
        console.log('表单验证失败，显示验证错误');
        form.reportValidity();
        return;
    }
    
    const formData = new FormData(form);
    const emailConfig = Object.fromEntries(formData.entries());
    
    // 将复选框值转换为布尔值
    emailConfig.enabled = !!formData.get('enabled');
    emailConfig.use_date_folder = !!formData.get('use_date_folder');
    
    console.log('准备保存的邮箱配置数据:', emailConfig);
    
    // 显示加载状态
    const saveBtn = document.getElementById('saveEmailConfigBtn');
    const originalText = saveBtn.innerHTML;
    saveBtn.disabled = true;
    saveBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> 保存中...';
    
    const isNew = !emailConfig.id;
    const method = isNew ? 'POST' : 'PUT';
    const url = isNew ? '/api/email_configs' : `/api/email_configs/${emailConfig.id}`;
    
    console.log('发送请求到:', url, '方法:', method);
    
    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(emailConfig)
    })
    .then(response => {
        console.log('收到响应状态码:', response.status, '状态文本:', response.statusText);
        if (!response.ok) {
            return response.text().then(text => {
                console.error('响应内容:', text);
                let errorMessage = '保存邮箱配置失败，HTTP状态码: ' + response.status;
                try {
                    // 尝试解析JSON
                    const data = JSON.parse(text);
                    errorMessage = data.message || errorMessage;
                } catch (e) {
                    // 如果不是JSON，使用原始文本
                    if (text) errorMessage = text;
                }
                throw new Error(errorMessage);
            });
        }
        return response.json();
    })
    .then(data => {
        console.log('响应数据:', data);
        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('emailConfigModal'));
        modal.hide();
        
        // 刷新邮箱配置列表
        initializeEmailConfigs();
        
        showAlert('success', '邮箱配置保存成功');
        
        // 如果是编辑现有配置，刷新任务状态
        if (emailConfig.id) {
            setTimeout(() => {
                loadTaskStatus(emailConfig.id);
            }, 1000);
        }
    })
    .catch(error => {
        console.error('保存邮箱配置出错:', error);
        showAlert('error', '保存邮箱配置失败: ' + error.message);
    })
    .finally(() => {
        // 恢复按钮状态
        saveBtn.disabled = false;
        saveBtn.innerHTML = originalText;
    });
}

// 测试邮箱连接
function testEmailConnection() {
    const form = document.getElementById('emailConfigForm');
    
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }
    
    // 禁用按钮，显示加载中
    const btn = document.getElementById('testConnectionBtn');
    const originalText = btn.innerHTML;
    btn.disabled = true;
    btn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> 测试中...';
    
    // 获取表单数据
    const formData = new FormData(form);
    const emailConfig = Object.fromEntries(formData.entries());
    
    // 将复选框值转换为布尔值
    emailConfig.enabled = !!formData.get('enabled');
    emailConfig.use_date_folder = !!formData.get('use_date_folder');
    
    // 检查是否有ID，如果有ID则使用ID进行测试，否则直接测试连接
    const id = document.getElementById('emailConfigId').value;
    
    if (id) {
        // 已有配置，使用ID测试
        fetch(`/api/email_configs/${id}/test`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                showAlert('success', '邮箱连接成功');
            } else {
                // 根据错误信息提供解决方案
                let errorMsg = data.message;
                let helpMsg = getEmailHelpMessage(errorMsg);
                
                // 显示包含帮助信息的错误提示
                if (helpMsg) {
                    showAlert('error', `${errorMsg}<br><strong>可能的解决方案:</strong><br>${helpMsg}`);
                } else {
                    showAlert('error', errorMsg);
                }
            }
        })
        .catch(error => {
            console.error('测试邮箱连接出错:', error);
            showAlert('error', '测试邮箱连接出错: ' + error.message);
        })
        .finally(() => {
            // 恢复按钮状态
            btn.disabled = false;
            btn.innerHTML = originalText;
        });
    } else {
        // 新建配置，直接测试连接参数
        fetch('/api/email_configs/test-connection', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(emailConfig)
        })
        .then(response => {
            if (!response.ok) {
                return response.json().then(data => {
                    throw new Error(data.message || '测试连接失败');
                });
            }
            return response.json();
        })
        .then(data => {
            showAlert('success', '邮箱连接测试成功');
        })
        .catch(error => {
            console.error('Error:', error);
            
            // 根据错误信息提供解决方案
            let errorMsg = error.message;
            let helpMsg = getEmailHelpMessage(errorMsg);
            
            // 显示包含帮助信息的错误提示
            if (helpMsg) {
                showAlert('error', `${errorMsg}<br><strong>可能的解决方案:</strong><br>${helpMsg}`);
            } else {
                showAlert('error', errorMsg);
            }
        })
        .finally(() => {
            // 恢复按钮状态
            btn.disabled = false;
            btn.innerHTML = originalText;
        });
    }
}

// 根据邮箱错误信息提供帮助指导
function getEmailHelpMessage(errorMsg) {
    errorMsg = errorMsg.toLowerCase();
    
    if (errorMsg.includes('connection refused') || errorMsg.includes('连接被拒绝')) {
        return `
            1. 检查服务器地址和端口是否正确<br>
            2. 确保网络环境允许连接到该服务器（如公司防火墙可能阻止）<br>
            3. 联系网络管理员确认是否可以访问该邮箱服务器
        `;
    }
    
    if (errorMsg.includes('无法解析主机名') || errorMsg.includes('gaierror')) {
        return `
            1. 检查服务器地址拼写是否正确<br>
            2. 确认网络连接是否正常<br>
            3. 可以尝试使用IP地址代替主机名
        `;
    }
    
    if (errorMsg.includes('ssl') || errorMsg.includes('证书')) {
        return `
            1. 检查端口号是否正确（通常SSL端口为993）<br>
            2. 确认邮箱服务器支持SSL连接<br>
            3. 如果是企业内部邮箱，可能需要安装相应的证书
        `;
    }
    
    if (errorMsg.includes('login failed') || errorMsg.includes('登录失败') || errorMsg.includes('授权码')) {
        return `
            1. 检查邮箱地址拼写是否正确<br>
            2. 确认输入的是<strong>授权码</strong>而非登录密码<br>
            3. 重新获取授权码: 登录网易企业邮箱 → 设置 → 账户 → POP3/IMAP/SMTP服务 → 开启IMAP服务并获取授权码<br>
            4. 确认企业邮箱已开启IMAP服务
        `;
    }
    
    if (errorMsg.includes('timeout') || errorMsg.includes('超时')) {
        return `
            1. 检查网络连接是否稳定<br>
            2. 服务器响应较慢，可以稍后重试<br>
            3. 检查防火墙或网络代理设置
        `;
    }
    
    // 默认帮助信息
    return `
        1. 确认邮箱服务器地址和端口是否正确<br>
        2. 检查邮箱账号和授权码<br>
        3. 确认企业邮箱已开启IMAP服务<br>
        4. 参考<a href="README_Email_Attachment.md" target="_blank">使用说明</a>中的"常见问题解答"部分
    `;
}

// 显示提示信息
function showAlert(type, message, timeout = 5000) {
    // 防止重复显示相同的提示
    const existingAlerts = document.querySelectorAll('.alert');
    for (const alert of existingAlerts) {
        // 检查是否已经有内容相同的提示信息
        if (alert.textContent.trim().includes(message.trim())) {
            return; // 如果已有相同内容的提示，则不再显示
        }
    }
    
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    // 添加到alertContainer中，而不是container-fluid的开头
    const alertContainer = document.getElementById('alertContainer');
    alertContainer.appendChild(alertDiv);
    
    // 自动关闭
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, timeout);
}

// 绑定订单标签页事件
function bindOrderTabEvents() {
    console.log("订单数据标签页事件绑定");
    
    // 绑定刷新订单按钮点击事件
    $("#refreshOrdersBtn").on("click", function() {
        loadOrderData();
    });
    
    // 绑定查看订单详情点击事件（使用事件委托，因为表格行是动态生成的）
    $("#ordersTable").on("click", ".view-order-btn", function() {
        const orderId = $(this).data("id");
        viewOrderDetails(orderId);
    });
    
    // 绑定删除订单点击事件（使用事件委托）
    $("#ordersTable").on("click", ".delete-order-btn", function() {
        const orderId = $(this).data("id");
        if (confirm("确定要删除此订单吗？此操作不可撤销。")) {
            deleteOrder(orderId);
        }
    });
    
    // 绑定导出订单数据按钮点击事件
    $("#exportOrdersBtn").on("click", function() {
        exportOrderData();
    });
    
    // 初始加载订单数据
    loadOrderData();
}

/**
 * 初始化Excel映射下拉框
 */
function initializeExcelMappings() {
    console.log('开始获取Excel映射配置...');
    
    // 获取当前页面的基础URL
    const baseUrl = window.location.origin;
    const apiUrl = `${baseUrl}/api/excel_mappings`;
    
    console.log('请求API:', apiUrl);
    
    // 使用fetch请求获取映射配置
    fetch(apiUrl, {
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: 'same-origin'  // 确保发送cookies
    })
    .then(response => {
        console.log('Excel映射API响应状态:', response.status, response.statusText);
        if (!response.ok) {
            throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.status === 'success') {
            console.log('成功获取Excel映射配置:', data.data);
            excelMappings = data.data || [];
            
            // 确保数据非空
            if (!Array.isArray(excelMappings)) {
                console.error('API返回的excelMappings不是数组:', excelMappings);
                excelMappings = [];
            }
            
            // 填充下拉框
            populateExcelMappingSelect();
        } else {
            console.error('获取Excel映射配置失败:', data.message);
            showAlert('error', '获取Excel映射配置失败: ' + data.message);
        }
    })
    .catch(error => {
        console.error('获取Excel映射配置出错:', error);
        showAlert('error', '获取Excel映射配置出错: ' + error.message);
    });
}

/**
 * 填充Excel映射配置下拉框
 */
function populateExcelMappingSelect() {
    const select = document.getElementById('excelMappingSelect');
    if (!select) return;
    
    // 清空现有选项
    select.innerHTML = '<option value="">请选择Excel映射配置...</option>';
    
    // 填充选项
    for (const mapping of excelMappings) {
        const option = document.createElement('option');
        option.value = mapping.id;
        option.textContent = mapping.name;
        select.appendChild(option);
    }
}

/**
 * 添加字段映射行
 */
function addFieldMappingRow() {
    // 克隆模板行
    const template = document.getElementById('fieldMappingTemplate');
    if (!template) {
        console.error('找不到字段映射模板行');
        return;
    }
    
    const newRow = template.cloneNode(true);
    newRow.id = '';
    newRow.style.display = '';
    
    // 绑定删除按钮事件
    const removeBtn = newRow.querySelector('.remove-field-btn');
    if (removeBtn) {
        removeBtn.addEventListener('click', function() {
            if (confirm('确定要删除此字段映射吗？')) {
                newRow.remove();
            }
        });
    }
    
    // 添加到表格中
    template.parentNode.appendChild(newRow);
    
    // 自动聚焦第一个输入框
    const firstInput = newRow.querySelector('input');
    if (firstInput) {
        firstInput.focus();
    }
    
    return newRow;
}

/**
 * 添加字段映射行并设置值
 */
function addFieldMappingRowWithValues(excelField, systemField) {
    // 首先添加一个空行
    const newRow = addFieldMappingRow();
    
    if (newRow) {
        const excelInput = newRow.querySelector('.excel-field');
        const systemInput = newRow.querySelector('.system-field');
        
        if (excelInput && systemInput) {
            excelInput.value = excelField;
            systemInput.value = systemField;
        }
    }
}

/**
 * 清空字段映射表格
 */
function clearFieldMappingTable() {
    // 选择正确的表格主体 - fieldMappingTable中除了模板行之外的所有行
    const tbody = document.querySelector('#fieldMappingTable tbody');
    if (tbody) {
        // 保留模板行，删除其他行
        const rows = tbody.querySelectorAll('tr:not(#fieldMappingTemplate)');
        rows.forEach(row => row.remove());
    }
}

/**
 * 清空Excel映射表单
 */
function clearExcelMappingForm() {
    const form = document.getElementById('excelMappingForm');
    form.reset();
    document.getElementById('excelMappingId').value = '';
    
    // 设置默认值
    document.getElementById('headerRow').value = '1';
    document.getElementById('startRow').value = '2';
    document.getElementById('outputFilename').value = '订单汇总_{date}.xlsx';
    document.getElementById('outputPath').value = 'downloads';
    document.getElementById('dateFormat').value = '%Y-%m-%d';
    
    // 清空字段映射表格
    clearFieldMappingTable();
}

/**
 * 保存Excel映射配置
 */
function saveExcelMapping() {
    const form = document.getElementById('excelMappingForm');
    
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }
    
    // 禁用保存按钮，显示加载中
    const saveBtn = document.getElementById('saveExcelMappingBtn');
    const originalText = saveBtn.innerHTML;
    saveBtn.disabled = true;
    saveBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> 保存中...';
    
    // 收集表单数据
    const formData = new FormData(form);
    const mappingData = {
        id: formData.get('id'),
        name: formData.get('name'),
        sheet_name: formData.get('sheet_name'),
        header_row: parseInt(formData.get('header_row')),
        start_row: parseInt(formData.get('start_row')),
        key_fields: formData.get('key_fields'),
        group_fields: formData.get('group_fields'),
        sort_fields: formData.get('sort_fields'),
        output_filename: formData.get('output_filename'),
        output_path: formData.get('output_path'),
        date_format: formData.get('date_format'),
        field_mappings: {}
    };
    
    // 收集字段映射数据
    const rows = document.querySelectorAll('#fieldMappingTable tbody tr:not(#fieldMappingTemplate)');
    for (const row of rows) {
        const excelField = row.querySelector('.excel-field').value.trim();
        const systemField = row.querySelector('.system-field').value.trim();
        
        if (excelField && systemField) {
            mappingData.field_mappings[excelField] = systemField;
        }
    }
    
    // 验证字段映射不为空
    if (Object.keys(mappingData.field_mappings).length === 0) {
        showAlert('warning', '请至少添加一个字段映射');
        saveBtn.disabled = false;
        saveBtn.innerHTML = originalText;
        return;
    }
    
    // 发送请求
    const isNew = !mappingData.id;
    const method = isNew ? 'POST' : 'PUT';
    const url = isNew ? '/api/excel_mappings' : `/api/excel_mappings/${mappingData.id}`;
    
    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(mappingData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('excelMappingModal'));
            modal.hide();
            
            // 重新加载Excel映射配置
            initializeExcelMappings();
            
            showAlert('success', 'Excel映射配置保存成功');
        } else {
            showAlert('error', '保存Excel映射配置失败: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('error', '保存Excel映射配置出错: ' + error.message);
    })
    .finally(() => {
        // 恢复按钮状态
        saveBtn.disabled = false;
        saveBtn.innerHTML = originalText;
    });
}

/**
 * 编辑Excel映射配置
 */
function editExcelMapping(id) {
    const mapping = excelMappings.find(m => m.id == id);
    if (!mapping) return;
    
    // 填充表单
    document.getElementById('excelMappingId').value = mapping.id;
    document.getElementById('mappingName').value = mapping.name;
    document.getElementById('sheetName').value = mapping.sheet_name || '';
    document.getElementById('headerRow').value = mapping.header_row;
    document.getElementById('startRow').value = mapping.start_row;
    document.getElementById('keyFields').value = mapping.key_fields;
    document.getElementById('groupFields').value = mapping.group_fields || '';
    document.getElementById('sortFields').value = mapping.sort_fields || '';
    document.getElementById('outputFilename').value = mapping.output_filename;
    document.getElementById('outputPath').value = mapping.output_path;
    document.getElementById('dateFormat').value = mapping.date_format;
    
    // 清空字段映射表格
    clearFieldMappingTable();
    
    // 填充字段映射
    let fieldMappings = {};
    try {
        if (typeof mapping.field_mappings === 'string') {
            fieldMappings = JSON.parse(mapping.field_mappings);
        } else if (typeof mapping.field_mappings === 'object') {
            fieldMappings = mapping.field_mappings;
        }
    } catch (e) {
        console.error('解析字段映射出错:', e);
        fieldMappings = {};
    }
    
    // 添加字段映射行
    for (const [excelField, systemField] of Object.entries(fieldMappings)) {
        addFieldMappingRowWithValues(excelField, systemField);
    }
    
    document.getElementById('excelMappingModalLabel').textContent = '编辑Excel映射配置';
    
    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('excelMappingModal'));
    modal.show();
}

/**
 * 加载订单数据
 */
function loadOrderData() {
    // 显示加载中状态
    $("#ordersTableBody").html('<tr><td colspan="10" class="text-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">加载中...</span></div></td></tr>');
    $("#refreshOrdersBtn").prop("disabled", true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 加载中...');
    
    // 获取订单数据
    $.ajax({
        url: "/api/order_data",
        type: "GET",
        dataType: "json",
        success: function(response) {
            if (response.status === "success") {
                renderOrdersTable(response.data);
                showAlert("success", `成功加载 ${response.data.length} 条订单数据`);
            } else {
                showAlert("danger", "加载订单数据失败: " + response.message);
                $("#ordersTableBody").html('<tr><td colspan="10" class="text-center text-danger">加载订单数据失败</td></tr>');
            }
        },
        error: function(xhr, status, error) {
            showAlert("danger", "获取订单数据时出错: " + error);
            $("#ordersTableBody").html('<tr><td colspan="10" class="text-center text-danger">获取订单数据时出错</td></tr>');
        },
        complete: function() {
            // 恢复按钮状态
            $("#refreshOrdersBtn").prop("disabled", false).html('<i class="fas fa-sync-alt me-1"></i>刷新');
        }
    });
}

/**
 * 渲染订单数据表格
 * @param {Array} orders 订单数据数组
 */
function renderOrdersTable(orders) {
    const tbody = $("#ordersTableBody");
    tbody.empty();
    
    if (orders.length === 0) {
        tbody.html('<tr><td colspan="10" class="text-center">暂无订单数据</td></tr>');
        return;
    }
    
    orders.forEach(function(order) {
        const row = $("<tr>");
        
        row.append(`<td>${order.order_number || ""}</td>`);
        row.append(`<td>${order.customer || ""}</td>`);
        row.append(`<td>${order.product_code || ""} ${order.product_name || ""}</td>`);
        row.append(`<td>${formatValue(order.quantity)}</td>`);
        row.append(`<td>${formatDate(order.delivery_date)}</td>`);
        row.append(`<td><span class="badge ${getStatusBadgeClass(order.status)}">${formatStatus(order.status)}</span></td>`);
        row.append(`
            <td>
                <div class="btn-group btn-group-sm" role="group">
                    <button type="button" class="btn btn-outline-primary view-order-btn" data-id="${order.id}" title="查看详情">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button type="button" class="btn btn-outline-danger delete-order-btn" data-id="${order.id}" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `);
        
        tbody.append(row);
    });
}

/**
 * 格式化数值
 * @param {*} value 需要格式化的值
 * @returns {string} 格式化后的字符串
 */
function formatValue(value) {
    if (value === null || value === undefined || value === "") {
        return "";
    }
    
    if (typeof value === "number") {
        // 如果是整数或整数型浮点数，不显示小数部分
        if (Number.isInteger(value)) {
            return value.toString();
        }
        // 否则保留2位小数
        return value.toFixed(2);
    }
    
    return value.toString();
}

/**
 * 格式化日期
 * @param {string} dateStr 日期字符串
 * @returns {string} 格式化后的日期字符串
 */
function formatDate(dateStr) {
    if (!dateStr) {
        return "";
    }
    
    // 已经是格式化的日期字符串直接返回
    if (typeof dateStr === "string" && dateStr.match(/^\d{4}-\d{2}-\d{2}$/)) {
        return dateStr;
    }
    
    try {
        const date = new Date(dateStr);
        if (isNaN(date.getTime())) {
            return dateStr;
        }
        
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        
        return `${year}-${month}-${day}`;
    } catch (e) {
        return dateStr;
    }
}

/**
 * 查看订单详情
 * @param {number} orderId 订单ID
 */
function viewOrderDetails(orderId) {
    // 显示加载中状态
    $("#orderDetailsBody").html('<div class="text-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">加载中...</span></div></div>');
    $("#orderDetailsModal").modal("show");
    
    // 获取订单详情
    $.ajax({
        url: `/api/order_data/${orderId}`,
        type: "GET",
        dataType: "json",
        success: function(response) {
            if (response.status === "success") {
                const order = response.data;
                
                // 构建详情HTML
                let detailsHtml = '<div class="table-responsive"><table class="table table-striped table-hover">';
                
                // 基本信息
                detailsHtml += '<thead><tr><th colspan="2" class="bg-primary text-white">基本信息</th></tr></thead><tbody>';
                detailsHtml += `<tr><th width="30%">订单ID</th><td>${order.id}</td></tr>`;
                detailsHtml += `<tr><th>订单号</th><td>${order.order_number || ""}</td></tr>`;
                detailsHtml += `<tr><th>客户名称</th><td>${order.customer || ""}</td></tr>`;
                detailsHtml += `<tr><th>订单状态</th><td>${order.status || ""}</td></tr>`;
                detailsHtml += `<tr><th>订单日期</th><td>${formatDate(order.order_date)}</td></tr>`;
                detailsHtml += `<tr><th>交付日期</th><td>${formatDate(order.delivery_date)}</td></tr>`;
                
                // 产品信息
                detailsHtml += '<thead><tr><th colspan="2" class="bg-primary text-white">产品信息</th></tr></thead>';
                detailsHtml += `<tr><th>产品编码</th><td>${order.product_code || ""}</td></tr>`;
                detailsHtml += `<tr><th>产品名称</th><td>${order.product_name || ""}</td></tr>`;
                detailsHtml += `<tr><th>数量</th><td>${formatValue(order.quantity)}</td></tr>`;
                detailsHtml += `<tr><th>单价</th><td>${formatValue(order.unit_price)}</td></tr>`;
                detailsHtml += `<tr><th>总价</th><td>${formatValue(order.total_price)}</td></tr>`;
                
                // 系统信息
                detailsHtml += '<thead><tr><th colspan="2" class="bg-primary text-white">系统信息</th></tr></thead>';
                detailsHtml += `<tr><th>数据来源</th><td>${order.source_file || ""}</td></tr>`;
                detailsHtml += `<tr><th>创建时间</th><td>${order.created_at || ""}</td></tr>`;
                detailsHtml += `<tr><th>更新时间</th><td>${order.updated_at || ""}</td></tr>`;
                
                detailsHtml += '</tbody></table></div>';
                
                // 显示详情
                $("#orderDetailsBody").html(detailsHtml);
                $("#orderDetailsModalLabel").text(`订单详情 - ${order.order_number || order.id}`);
            } else {
                $("#orderDetailsBody").html(`<div class="alert alert-danger">获取订单详情失败: ${response.message}</div>`);
            }
        },
        error: function(xhr, status, error) {
            $("#orderDetailsBody").html(`<div class="alert alert-danger">获取订单详情出错: ${error}</div>`);
        }
    });
}

/**
 * 删除订单
 * @param {number} orderId 订单ID
 */
function deleteOrder(orderId) {
    // 显示加载中状态
    const row = $(`#ordersTable .delete-order-btn[data-id="${orderId}"]`).closest("tr");
    row.addClass("table-warning");
    
    // 发送删除请求
    $.ajax({
        url: `/api/order_data/${orderId}`,
        type: "DELETE",
        dataType: "json",
        success: function(response) {
            if (response.status === "success") {
                // 移除行
                row.fadeOut(300, function() {
                    $(this).remove();
                    
                    // 检查表格是否为空
                    if ($("#ordersTableBody tr").length === 0) {
                        $("#ordersTableBody").html('<tr><td colspan="10" class="text-center">暂无订单数据</td></tr>');
                    }
                });
                
                showAlert("success", "订单删除成功");
            } else {
                row.removeClass("table-warning");
                showAlert("danger", "删除订单失败: " + response.message);
            }
        },
        error: function(xhr, status, error) {
            row.removeClass("table-warning");
            showAlert("danger", "删除订单时出错: " + error);
        }
    });
}

/**
 * 导出订单数据
 */
function exportOrderData() {
    $("#exportOrdersBtn").prop("disabled", true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 导出中...');
    
    // 创建一个隐藏的iframe来处理下载
    const iframe = document.createElement("iframe");
    iframe.style.display = "none";
    iframe.src = "/api/order_data/export";
    document.body.appendChild(iframe);
    
    // 恢复按钮状态
    setTimeout(function() {
        $("#exportOrdersBtn").prop("disabled", false).html('导出订单数据');
        showAlert("success", "订单数据导出请求已发送，如果导出成功，文件将自动下载");
        
        // 移除iframe
        setTimeout(function() {
            document.body.removeChild(iframe);
        }, 5000);
    }, 2000);
}

// 获取状态徽章样式类
function getStatusBadgeClass(status) {
    switch (status) {
        case 'new':
            return 'bg-primary';
        case 'processing':
            return 'bg-warning';
        case 'completed':
            return 'bg-success';
        case 'cancelled':
            return 'bg-danger';
        default:
            return 'bg-secondary';
    }
}

// 格式化状态显示文本
function formatStatus(status) {
    const statusMap = {
        'new': '新建',
        'processing': '处理中',
        'completed': '已完成',
        'cancelled': '已取消'
    };
    return statusMap[status] || status;
}

// 清空字段映射表格
function clearFieldMappingTable() {
    // 选择正确的表格主体 - fieldMappingTable中除了模板行之外的所有行
    const tbody = document.querySelector('#fieldMappingTable tbody');
    if (tbody) {
        // 保留模板行，删除其他行
        const rows = tbody.querySelectorAll('tr:not(#fieldMappingTemplate)');
        rows.forEach(row => row.remove());
    }
}

// 初始化配置标签页
function initializeConfigTab() {
    console.log('初始化配置标签页...');
    
    // 添加全局错误处理，帮助捕获API请求错误
    window.addEventListener('error', function(event) {
        console.error('全局错误:', event.error);
    });
    
    // 加载邮箱配置列表
    try {
        console.log('开始加载邮箱配置...');
        initializeEmailConfigs();
    } catch (error) {
        console.error('初始化邮箱配置出错:', error);
        showAlert('error', '初始化邮箱配置失败，请查看控制台了解详情');
    }
    
    console.log('配置标签页初始化完成');
}

// 加载附件列表
function loadAttachments() {
    const attachmentsTableBody = document.getElementById('attachmentsTableBody');
    if (!attachmentsTableBody) return;
    
    // 获取选中的邮箱配置ID
    const configId = document.getElementById('emailConfigSelect').value;
    if (!configId) {
        // 无选中配置，清空表格
        attachmentsTableBody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center">
                    <p class="my-3">请先选择一个邮箱配置</p>
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="document.getElementById('emailConfigSelect').focus()">
                        <i class="fas fa-arrow-up me-1"></i>选择邮箱配置
                    </button>
                </td>
            </tr>
        `;
        return;
    }
    
    // 显示加载中
    attachmentsTableBody.innerHTML = `
        <tr>
            <td colspan="6" class="text-center">
                <div class="spinner-border text-primary" role="status"></div>
                <p class="mt-2">正在加载附件列表...</p>
            </td>
        </tr>
    `;
    
    // 获取附件列表
    fetch(`/api/email_attachments?config_id=${configId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('获取附件列表失败');
            }
            return response.json();
        })
        .then(result => {
            if (result.status === 'success') {
                // 更新全局变量
                emailAttachments = result.data;
                // 渲染表格
                renderAttachmentsTable();
            } else {
                throw new Error(result.message || '获取数据失败');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            attachmentsTableBody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center text-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        ${error.message}
                    </td>
                </tr>
            `;
            showAlert('error', '加载附件列表失败: ' + error.message);
        });
}

// 渲染附件表格
function renderAttachmentsTable() {
    const tableBody = document.getElementById('attachmentsTableBody');
    if (!tableBody) return;
    
    if (emailAttachments.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center">
                    <p class="my-3">暂无附件数据</p>
                </td>
            </tr>
        `;
        return;
    }
    
    // 构建表格HTML
    let html = '';
    
    for (const attachment of emailAttachments) {
        // 使用API返回的状态信息
        const processed = attachment.processed;
        const processClass = attachment.status_class || '';
        const processIcon = attachment.status_icon || '';
        const processText = attachment.status_text || (processed ? '已处理' : '未处理');
        const isDownloaded = attachment.downloaded;
        
        html += `
            <tr>
                <td>${attachment.filename || ''}</td>
                <td>${attachment.sender || ''}</td>
                <td>${formatDate(attachment.receive_date) || ''}</td>
                <td>
                    <span class="${processClass}">
                        ${processIcon ? `<i class="fas ${processIcon} me-1"></i>` : ''}
                        ${processText}
                    </span>
                    ${attachment.process_message ? `<i class="fas fa-info-circle ms-1" title="${attachment.process_message}" style="cursor: help;"></i>` : ''}
                </td>
                <td>${processed ? formatDate(attachment.process_date) : ''}</td>
                <td>
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-primary process-attachment-btn" 
                                data-id="${attachment.id}" 
                                ${processed && attachment.process_result === 'success' ? 'disabled' : ''}
                                ${attachment.process_result === 'skipped' ? 'data-skipped="true"' : ''}
                                title="${processed ? getProcessButtonTitle(attachment.process_result) : '处理附件'}">
                            <i class="fas ${attachment.process_result === 'skipped' ? 'fa-download' : 'fa-cogs'}"></i>
                        </button>
                        ${isDownloaded ? `
                        <a href="/api/email_attachments/${attachment.id}/download" class="btn btn-outline-success" 
                           title="下载附件" target="_blank">
                            <i class="fas fa-download"></i>
                        </a>
                        ` : ''}
                    </div>
                </td>
            </tr>
        `;
    }
    
    tableBody.innerHTML = html;
    
    // 更新附件计数
    const count = document.getElementById('attachmentCount');
    if (count) {
        count.textContent = emailAttachments.length;
    }
    
    // 添加处理附件按钮的点击事件
    document.querySelectorAll('.process-attachment-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const attachmentId = this.getAttribute('data-id');
            const isSkipped = this.getAttribute('data-skipped') === 'true';
            
            if (isSkipped) {
                // 对于跳过的附件，直接下载
                downloadSkippedAttachment(attachmentId);
            } else {
                // 正常处理附件
                showProcessAttachmentModal(attachmentId);
            }
        });
    });
}

// 下载跳过的附件
function downloadSkippedAttachment(attachmentId) {
    // 显示加载中
    showAlert('info', '正在下载附件...');
    
    // 直接重定向到下载URL
    window.location.href = `/api/email_attachments/${attachmentId}/download`;
}

// 显示处理附件模态框
function showProcessAttachmentModal(attachmentId) {
    // 查找附件
    const attachment = emailAttachments.find(a => a.id == attachmentId);
    if (!attachment) {
        showAlert('error', '找不到附件信息');
        return;
    }
    
    // 填充模态框
    document.getElementById('attachmentId').value = attachment.id;
    document.getElementById('attachmentFilename').textContent = attachment.filename || '';
    document.getElementById('attachmentSender').textContent = attachment.sender || '';
    document.getElementById('attachmentReceiveDate').textContent = formatDate(attachment.receive_date) || '';
    
    // 加载Excel映射配置下拉框
    populateExcelMappingSelect();
    
    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('processAttachmentModal'));
    modal.show();
    
    // 绑定处理按钮事件
    document.getElementById('processAttachmentBtn').onclick = function() {
        processAttachment();
    };
}

// 处理附件
function processAttachment() {
    const form = document.getElementById('processAttachmentForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }
    
    const attachmentId = document.getElementById('attachmentId').value;
    const mappingId = document.getElementById('mappingSelect').value;
    
    if (!attachmentId || !mappingId) {
        showAlert('error', '缺少必要参数');
        return;
    }
    
    // 禁用按钮，显示加载中
    const btn = document.getElementById('processAttachmentBtn');
    const originalText = btn.innerHTML;
    btn.disabled = true;
    btn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> 处理中...';
    
    // 发送请求
    fetch(`/api/email_attachments/${attachmentId}/process`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            mapping_id: mappingId
        })
    })
    .then(response => response.json())
    .then(data => {
        // 隐藏模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('processAttachmentModal'));
        modal.hide();
        
        if (data.status === 'success') {
            showAlert('success', data.message);
            // 重新加载附件列表
            loadAttachments();
        } else {
            showAlert('error', data.message);
        }
    })
    .catch(error => {
        console.error('处理附件出错:', error);
        showAlert('error', '处理附件失败: ' + error.message);
    })
    .finally(() => {
        // 恢复按钮状态
        btn.disabled = false;
        btn.innerHTML = originalText;
    });
}

// 填充Excel映射配置下拉框
function populateExcelMappingSelect() {
    const select = document.getElementById('mappingSelect');
    if (!select) return;
    
    // 清空下拉框
    select.innerHTML = '<option value="">请选择...</option>';
    
    // 填充下拉框
    for (const mapping of excelMappings) {
        const option = document.createElement('option');
        option.value = mapping.id;
        option.textContent = mapping.name;
        select.appendChild(option);
    }
}

// 获取处理按钮的提示文本
function getProcessButtonTitle(processResult) {
    switch (processResult) {
        case 'success':
            return '已处理';
        case 'skipped':
            return '已跳过，点击下载';
        case 'error':
            return '处理失败，点击重试';
        default:
            return '处理附件';
    }
}

// 初始化模态框拖动功能
function initDraggableModals() {
    // 确保所有可拖动模态框初始状态是默认样式
    document.querySelectorAll('.modal-dialog-draggable').forEach(function(dialog) {
        dialog.style.position = '';
        dialog.style.left = '';
        dialog.style.top = '';
        dialog.style.margin = '';
        dialog.style.transform = '';
    });
    
    // 遍历所有可拖动的模态框
    document.querySelectorAll('.modal-dialog-draggable').forEach(function(dialog) {
        let isDragging = false;
        let offsetX, offsetY;
        
        // 找到对应模态框的头部
        const header = dialog.querySelector('.modal-header');
        if (!header) return;
        
        // 鼠标按下事件
        header.addEventListener('mousedown', function(e) {
            isDragging = true;
            offsetX = e.clientX - dialog.getBoundingClientRect().left;
            offsetY = e.clientY - dialog.getBoundingClientRect().top;
            
            // 添加"正在拖动"类
            dialog.classList.add('dragging');
        });
        
        // 鼠标移动事件
        document.addEventListener('mousemove', function(e) {
            if (!isDragging) return;
            
            // 计算新位置
            const left = e.clientX - offsetX;
            const top = e.clientY - offsetY;
            
            // 设置模态框位置 - 当开始拖动后才设置具体位置
            dialog.style.position = 'fixed'; // 拖动时使用fixed定位
            dialog.style.left = `${left}px`;
            dialog.style.top = `${top}px`;
            dialog.style.margin = '0'; // 拖动时取消margin
            dialog.style.transform = 'none'; // 取消bootstrap的默认居中样式
        });
        
        // 鼠标释放事件
        document.addEventListener('mouseup', function() {
            isDragging = false;
            dialog.classList.remove('dragging');
        });
    });
    
    // 当模态框显示时不要强制设置位置，让Bootstrap默认居中
    document.querySelectorAll('.modal').forEach(function(modal) {
        modal.addEventListener('hide.bs.modal', function() {
            const dialog = this.querySelector('.modal-dialog-draggable');
            if (dialog) {
                // 模态框关闭时重置样式，恢复Bootstrap默认居中
                dialog.style.position = '';
                dialog.style.left = '';
                dialog.style.top = '';
                dialog.style.margin = '';
                dialog.style.transform = '';
            }
        });
    });
}

// 添加字段映射行
function addFieldMappingRow() {
    // 克隆模板行
    const template = document.getElementById('fieldMappingTemplate');
    if (!template) {
        console.error('找不到字段映射模板行');
        return;
    }
    
    const newRow = template.cloneNode(true);
    newRow.id = '';
    newRow.style.display = '';
    
    // 绑定删除按钮事件
    const removeBtn = newRow.querySelector('.remove-field-btn');
    if (removeBtn) {
        removeBtn.addEventListener('click', function() {
            if (confirm('确定要删除此字段映射吗？')) {
                newRow.remove();
            }
        });
    }
    
    // 添加到表格中
    template.parentNode.appendChild(newRow);
    
    // 自动聚焦第一个输入框
    const firstInput = newRow.querySelector('input');
    if (firstInput) {
        firstInput.focus();
    }
    
    return newRow;
}

// 获取新附件
function fetchNewAttachments() {
    // 获取选中的邮箱配置ID
    const configId = document.getElementById('emailConfigSelect').value;
    if (!configId) {
        showAlert('warning', '请先选择一个邮箱配置');
        return;
    }
    
    // 获取附件名称关键词
    const attachmentKeywords = document.getElementById('attachmentKeywords').value.trim();
    if (!attachmentKeywords) {
        if (!confirm('您未输入附件名称关键词，将获取所有Excel附件。是否继续？')) {
            return;
        }
    }
    
    // 禁用按钮，显示加载中
    const btn = document.getElementById('fetchAttachmentsBtn');
    const originalText = btn.innerHTML;
    btn.disabled = true;
    btn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> 获取中...';
    
    // 发送请求，获取最近7天的附件
    fetch(`/api/email_configs/${configId}/fetch`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            days: 7,  // 设置为获取最近30天的附件
            attachment_keywords: attachmentKeywords // 传递附件名称关键词
        })
    })
    .then(response => {
        if (!response.ok) {
            return response.json().then(data => {
                throw new Error(data.message || '获取新附件失败');
            });
        }
        return response.json();
    })
    .then(data => {
        if (data.status === 'success') {
            // 如果没有下载到附件但找到了邮件，提示用户检查配置
            if (data.data.total_emails > 0 && data.data.downloaded === 0) {
                showAlert('warning', '找到了邮件但未下载到任何附件，可能是发件人或主题过滤条件过于严格，或者附件名称关键词不匹配。建议检查邮箱配置中的过滤条件。');
            } else {
                showAlert('success', data.message);
            }
            
            // 显示详细结果
            showFetchResult(data);
            
            // 重新加载附件列表
            loadAttachments();
        } else {
            throw new Error(data.message || '获取新附件失败');
        }
    })
    .catch(error => {
        console.error('获取新附件出错:', error);
        showAlert('error', '获取新附件失败: ' + error.message);
    })
    .finally(() => {
        // 恢复按钮状态
        btn.disabled = false;
        btn.innerHTML = originalText;
    });
}

// 显示获取结果详情
function showFetchResult(data) {
    // 创建结果详情对话框
    let resultHtml = `
        <div class="modal fade" id="fetchResultModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">附件获取结果详情</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert ${data.data.downloaded > 0 ? 'alert-success' : 'alert-warning'}">
                            共搜索到 ${data.data.total_emails} 封邮件，处理了 ${data.data.total_attachments} 个附件，
                            其中下载成功 ${data.data.downloaded} 个，跳过 ${data.data.skipped} 个，失败 ${data.data.failed} 个。
                        </div>
                        
                        <ul class="nav nav-tabs" id="resultTab" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="downloaded-tab" data-bs-toggle="tab" data-bs-target="#downloaded" 
                                        type="button" role="tab" aria-controls="downloaded" aria-selected="true">
                                    下载成功 <span class="badge bg-success">${data.data.downloaded}</span>
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="skipped-tab" data-bs-toggle="tab" data-bs-target="#skipped" 
                                        type="button" role="tab" aria-controls="skipped" aria-selected="false">
                                    已跳过 <span class="badge bg-warning">${data.data.skipped}</span>
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="failed-tab" data-bs-toggle="tab" data-bs-target="#failed" 
                                        type="button" role="tab" aria-controls="failed" aria-selected="false">
                                    失败 <span class="badge bg-danger">${data.data.failed}</span>
                                </button>
                            </li>
                        </ul>
                        
                        <div class="tab-content p-3 border border-top-0 rounded-bottom" id="resultTabContent">
                            <!-- 下载成功的附件 -->
                            <div class="tab-pane fade show active" id="downloaded" role="tabpanel" aria-labelledby="downloaded-tab">
                                ${renderFileList(data.data.processed_files, '下载成功')}
                            </div>
                            
                            <!-- 跳过的附件 -->
                            <div class="tab-pane fade" id="skipped" role="tabpanel" aria-labelledby="skipped-tab">
                                ${renderFileList(data.data.skipped_files, '已跳过', true)}
                            </div>
                            
                            <!-- 失败的附件 -->
                            <div class="tab-pane fade" id="failed" role="tabpanel" aria-labelledby="failed-tab">
                                ${renderFileList(data.data.failed_files, '失败', true)}
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" data-bs-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // 添加到文档
    const modalContainer = document.createElement('div');
    modalContainer.innerHTML = resultHtml;
    document.body.appendChild(modalContainer);
    
    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('fetchResultModal'));
    modal.show();
    
    // 监听模态框关闭事件，移除DOM元素
    document.getElementById('fetchResultModal').addEventListener('hidden.bs.modal', function() {
        document.body.removeChild(modalContainer);
    });
}

// 渲染文件列表
function renderFileList(files, type, showReason = false) {
    if (!files || files.length === 0) {
        return `<div class="alert alert-info">没有${type}的附件</div>`;
    }
    
    let html = `
        <div class="table-responsive">
            <table class="table table-sm table-hover">
                <thead>
                    <tr>
                        <th>文件名</th>
                        ${showReason ? '<th>原因</th>' : '<th>大小</th>'}
                        <th>发件人</th>
                        <th>邮件主题</th>
                    </tr>
                </thead>
                <tbody>
    `;
    
    for (const file of files) {
        html += `
            <tr>
                <td>${file.filename || '未命名'}</td>
                ${showReason 
                    ? `<td>${file.reason || ''}</td>` 
                    : `<td>${formatFileSize(file.size || 0)}</td>`
                }
                <td>${file.sender || ''}</td>
                <td>${file.subject || ''}</td>
            </tr>
        `;
    }
    
    html += `
                </tbody>
            </table>
        </div>
    `;
    
    return html;
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 执行批量处理附件操作
 */
function batchProcessAttachments() {
    const select = document.getElementById('excelMappingSelect');
    const mappingId = select.value;
    
    if (!mappingId) {
        showAlert('warning', '请先选择Excel映射配置');
        return;
    }
    
    // 查找选中的配置
    const mapping = excelMappings.find(m => m.id == mappingId);
    if (!mapping) {
        showAlert('error', '找不到选中的映射配置');
        return;
    }
    
    // 确认操作
    if (!confirm(`确定要使用映射配置"${mapping.name}"批量处理所有未处理的附件吗？`)) {
        return;
    }
    
    // 显示加载中状态
    showAlert('info', '正在批量处理附件，请稍候...', false);
    
    // 禁用批量处理按钮
    const batchBtn = document.getElementById('batchProcessBtn');
    const originalBtnText = batchBtn.innerHTML;
    batchBtn.disabled = true;
    batchBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 处理中...';
    
    // 发送请求到API
    fetch('/api/email_attachments/batch_process', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            mapping_id: mappingId
        })
    })
    .then(response => response.json())
    .then(data => {
        // 恢复按钮状态
        batchBtn.disabled = false;
        batchBtn.innerHTML = originalBtnText;
        
        if (data.status === 'success') {
            // 更新处理结果显示
            document.getElementById('totalFiles').textContent = data.data.total_files;
            document.getElementById('successFiles').textContent = data.data.success_files;
            document.getElementById('errorFiles').textContent = data.data.error_files;
            document.getElementById('totalRecords').textContent = data.data.total_records;
            
            // 更新汇总文件信息
            if (data.data.summary_file) {
                document.getElementById('summaryFilePath').textContent = data.data.summary_file;
                document.getElementById('summaryFileAlert').style.display = 'block';
            } else {
                document.getElementById('summaryFileAlert').style.display = 'none';
            }
            
            // 显示成功消息
            showAlert('success', data.message);
            
            // 刷新附件列表
            loadAttachments();
        } else {
            showAlert('error', data.message || '批量处理失败');
        }
    })
    .catch(error => {
        // 恢复按钮状态
        batchBtn.disabled = false;
        batchBtn.innerHTML = originalBtnText;
        
        console.error('批量处理附件请求出错:', error);
        showAlert('error', '批量处理附件请求出错: ' + error.message);
    });
}

/**
 * 显示批量处理结果
 */
function showBatchProcessResults(results) {
    // 创建结果模态框
    if (!document.getElementById('batchResultModal')) {
        const modalHTML = `
        <div class="modal fade" id="batchResultModal" tabindex="-1" aria-labelledby="batchResultModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="batchResultModalLabel">批量处理结果</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body" id="batchResultContent">
                        <!-- 内容将通过JavaScript填充 -->
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        <button type="button" class="btn btn-primary" id="downloadSummaryBtn">下载汇总文件</button>
                    </div>
                </div>
            </div>
        </div>
        `;
        
        // 将模态框添加到body
        const div = document.createElement('div');
        div.innerHTML = modalHTML;
        document.body.appendChild(div.firstChild);
        
        // 绑定下载按钮事件
        document.getElementById('downloadSummaryBtn').addEventListener('click', function() {
            downloadSummaryFile();
        });
    }
    
    // 准备结果内容
    let content = '';
    
    // 1. 处理统计信息
    content += `
    <div class="alert alert-info">
        <h6><i class="fas fa-chart-bar me-2"></i>处理统计</h6>
        <div class="row g-2 mt-2">
            <div class="col-md-3">
                <div class="card bg-light">
                    <div class="card-body text-center p-2">
                        <h3>${results.total_files || 0}</h3>
                        <small class="text-muted">总文件数</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center p-2">
                        <h3>${results.success || 0}</h3>
                        <small>成功处理</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-danger text-white">
                    <div class="card-body text-center p-2">
                        <h3>${results.error || 0}</h3>
                        <small>处理失败</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center p-2">
                        <h3>${results.total_records || 0}</h3>
                        <small>总记录数</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    `;
    
    // 2. 汇总文件信息
    if (results.summary_file) {
        content += `
        <div class="alert alert-success">
            <h6><i class="fas fa-file-excel me-2"></i>汇总文件</h6>
            <p class="mb-2">${results.summary_file}</p>
            <button type="button" class="btn btn-sm btn-success" id="downloadSummaryInlineBtn">
                <i class="fas fa-download me-1"></i>下载汇总文件
            </button>
        </div>
        `;
        
        // 保存汇总文件路径到全局变量，用于下载
        window.summaryFilePath = results.summary_file;
    }
    
    // 3. 成功处理的文件列表
    if (results.processed_files && results.processed_files.length > 0) {
        content += `
        <h6 class="mt-4"><i class="fas fa-check-circle me-2 text-success"></i>成功处理的文件 (${results.processed_files.length})</h6>
        <div class="table-responsive">
            <table class="table table-sm table-striped">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>文件名</th>
                        <th>记录数</th>
                    </tr>
                </thead>
                <tbody>
        `;
        
        results.processed_files.forEach((file, index) => {
            const filename = file.file_path.split('/').pop();
            content += `
                <tr>
                    <td>${index + 1}</td>
                    <td title="${file.file_path}">${filename}</td>
                    <td>${file.records || 0}</td>
                </tr>
            `;
        });
        
        content += `
                </tbody>
            </table>
        </div>
        `;
    }
    
    // 4. 处理失败的文件列表
    if (results.failed_files && results.failed_files.length > 0) {
        content += `
        <h6 class="mt-4"><i class="fas fa-times-circle me-2 text-danger"></i>处理失败的文件 (${results.failed_files.length})</h6>
        <div class="table-responsive">
            <table class="table table-sm table-striped">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>文件名</th>
                        <th>错误信息</th>
                    </tr>
                </thead>
                <tbody>
        `;
        
        results.failed_files.forEach((file, index) => {
            const filename = file.file_path.split('/').pop();
            content += `
                <tr>
                    <td>${index + 1}</td>
                    <td title="${file.file_path}">${filename}</td>
                    <td>${file.error || '未知错误'}</td>
                </tr>
            `;
        });
        
        content += `
                </tbody>
            </table>
        </div>
        `;
    }
    
    // 更新模态框内容
    document.getElementById('batchResultContent').innerHTML = content;
    
    // 绑定内联下载按钮事件
    const inlineDownloadBtn = document.getElementById('downloadSummaryInlineBtn');
    if (inlineDownloadBtn) {
        inlineDownloadBtn.addEventListener('click', function() {
            downloadSummaryFile();
        });
    }
    
    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('batchResultModal'));
    modal.show();
    
    // 显示成功消息
    showAlert('success', `批量处理完成：成功 ${results.success} 个文件，共 ${results.total_records} 条记录`);
}

/**
 * 下载汇总文件
 */
function downloadSummaryFile() {
    if (!window.summaryFilePath) {
        showAlert('warning', '没有可下载的汇总文件');
        return;
    }
    
    // 构建下载URL
    const downloadUrl = `/api/order_data/download_summary?path=${encodeURIComponent(window.summaryFilePath)}`;
    
    // 创建下载链接并点击
    const a = document.createElement('a');
    a.href = downloadUrl;
    a.target = '_blank';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
}

// 绑定批量处理按钮事件
document.getElementById('batchProcessBtn').addEventListener('click', function() {
    batchProcessAttachments();
});

// 绑定下载汇总按钮事件
document.getElementById('downloadSummaryBtn').addEventListener('click', function(e) {
    e.preventDefault();
    const filePath = document.getElementById('summaryFilePath').textContent;
    if (filePath) {
        window.location.href = `/api/order_data/download_summary?path=${encodeURIComponent(filePath)}`;
    }
});

/**
 * 全局变量用于存储Excel映射配置
 */
let excelMappings = [];

/**
 * 页面加载完成后的初始化
 */
document.addEventListener('DOMContentLoaded', function() {
    // 初始化Excel映射配置
    initializeExcelMappings();
    
    // 绑定新增配置按钮事件
    document.getElementById('addMappingBtn').addEventListener('click', function() {
        showAddMappingModal();
    });
    
    // 绑定添加字段映射按钮事件
    document.getElementById('addFieldBtn').addEventListener('click', function() {
        addMappingField();
    });
    
    // 绑定保存配置按钮事件
    document.getElementById('saveMappingBtn').addEventListener('click', function() {
        saveMappingConfig();
    });
    
    // 绑定编辑配置按钮事件
    document.getElementById('editMappingBtn').addEventListener('click', function() {
        editMappingConfig();
    });
    
    // 绑定删除配置按钮事件
    document.getElementById('deleteMappingBtn').addEventListener('click', function() {
        deleteMappingConfig();
    });
    
    // 绑定批量处理按钮事件
    document.getElementById('batchProcessBtn').addEventListener('click', function() {
        batchProcessAttachments();
    });
    
    // 为选择框添加change事件
    const select = document.getElementById('excelMappingSelect');
    if (select) {
        select.addEventListener('change', updateConfigButtonsState);
    }
    
    // 绑定下载汇总按钮事件
    const downloadSummaryBtn = document.getElementById('downloadSummaryBtn');
    if (downloadSummaryBtn) {
        downloadSummaryBtn.addEventListener('click', function(e) {
            e.preventDefault();
            const filePath = document.getElementById('summaryFilePath').textContent;
            if (filePath) {
                window.location.href = `/api/order_data/download_summary?path=${encodeURIComponent(filePath)}`;
            }
        });
    }
});

/**
 * 初始化Excel映射配置
 */
function initializeExcelMappings() {
    // 尝试从后端API加载配置
    fetch('/api/excel_mappings')
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                console.log('成功从API加载Excel映射配置:', data.data);
                excelMappings = data.data;
                populateExcelMappingSelect();
                updateConfigButtonsState(); // 更新按钮状态
            } else {
                console.warn('从API加载Excel映射配置失败:', data.message);
                // 如果API加载失败，尝试从LocalStorage加载
                loadFromLocalStorage();
            }
        })
        .catch(error => {
            console.error('从API加载Excel映射配置请求出错:', error);
            // 如果请求出错，尝试从LocalStorage加载
            loadFromLocalStorage();
        });
}

/**
 * 从LocalStorage加载配置
 */
function loadFromLocalStorage() {
    const savedMappings = localStorage.getItem('excelMappings');
    if (savedMappings) {
        try {
            excelMappings = JSON.parse(savedMappings);
            console.log('成功从LocalStorage加载Excel映射配置:', excelMappings);
            populateExcelMappingSelect();
            updateConfigButtonsState(); // 更新按钮状态
        } catch (error) {
            console.error('解析LocalStorage中的Excel映射配置失败:', error);
            createSampleMapping();
        }
    } else {
        console.log('没有找到保存的Excel映射配置，将创建示例配置');
        createSampleMapping();
    }
}

/**
 * 创建示例映射配置
 */
function createSampleMapping() {
    // 创建一个示例配置
    const sampleMapping = {
        id: Date.now(), // 使用时间戳作为ID
        name: '生产订单映射示例',
        sheet_name: 'Sheet1',
        header_row: 1,
        start_row: 2,
        key_fields: '订单号,产品编码',
        field_mappings: [
            { excel_field: '订单号', system_field: 'order_number' },
            { excel_field: '客户名称', system_field: 'customer_name' },
            { excel_field: '产品编码', system_field: 'product_code' },
            { excel_field: '产品名称', system_field: 'product_name' },
            { excel_field: '数量', system_field: 'quantity' },
            { excel_field: '交付日期', system_field: 'delivery_date' }
        ],
        date_format: 'YYYY-MM-DD'
    };
    
    excelMappings = [sampleMapping]; // 重置为只包含示例映射的数组
    saveExcelMappingsToStorage();
    populateExcelMappingSelect();
    updateConfigButtonsState(); // 更新按钮状态
}

/**
 * 保存Excel映射配置到LocalStorage
 */
function saveExcelMappingsToStorage() {
    localStorage.setItem('excelMappings', JSON.stringify(excelMappings));
}

/**
 * 填充Excel映射配置选择框
 */
function populateExcelMappingSelect() {
    const select = document.getElementById('excelMappingSelect');
    if (!select) return;
    
    // 清空现有选项
    select.innerHTML = '<option value="">请选择Excel映射配置...</option>';
    
    // 填充选项
    for (const mapping of excelMappings) {
        const option = document.createElement('option');
        option.value = mapping.id;
        option.textContent = mapping.name;
        select.appendChild(option);
    }
    
    // 处理编辑和删除按钮的状态
    updateConfigButtonsState();
}

/**
 * 更新配置相关按钮的状态
 */
function updateConfigButtonsState() {
    const select = document.getElementById('excelMappingSelect');
    const editBtn = document.getElementById('editMappingBtn');
    const deleteBtn = document.getElementById('deleteMappingBtn');
    const batchBtn = document.getElementById('batchProcessBtn');
    
    const isSelected = select.value !== '';
    
    editBtn.disabled = !isSelected;
    deleteBtn.disabled = !isSelected;
    batchBtn.disabled = !isSelected;
}

/**
 * 显示新增Excel映射配置模态框
 */
function showAddMappingModal() {
    // 清空表单
    document.getElementById('mappingForm').reset();
    document.getElementById('mappingFieldsContainer').innerHTML = '';
    
    // 添加初始的几个字段映射
    addMappingField('order_number', '订单号');
    addMappingField('customer_name', '客户名称');
    addMappingField('product_code', '产品编码');
    
    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('addMappingModal'));
    modal.show();
}

/**
 * 添加一行字段映射
 * @param {string} systemField 系统字段名
 * @param {string} excelColumn Excel列名
 */
function addMappingField(systemField = '', excelColumn = '') {
    const container = document.getElementById('mappingFieldsContainer');
    const fieldIndex = container.children.length;
    
    const rowDiv = document.createElement('div');
    rowDiv.className = 'row mb-2 align-items-center mapping-field-row';
    rowDiv.dataset.index = fieldIndex;
    
    rowDiv.innerHTML = `
        <div class="col-5">
            <input type="text" class="form-control form-control-sm system-field" 
                   name="systemField[${fieldIndex}]" value="${systemField}" required
                   placeholder="例如：order_number">
        </div>
        <div class="col-5">
            <input type="text" class="form-control form-control-sm excel-column" 
                   name="excelColumn[${fieldIndex}]" value="${excelColumn}" required
                   placeholder="例如：A 或 订单编号">
        </div>
        <div class="col-2">
            <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeMappingField(${fieldIndex})">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    container.appendChild(rowDiv);
}

/**
 * 删除字段映射
 * @param {number} index 字段索引
 */
function removeMappingField(index) {
    const row = document.querySelector(`.mapping-field-row[data-index="${index}"]`);
    if (row) {
        row.remove();
    }
}

/**
 * 保存Excel映射配置
 */
function saveMappingConfig() {
    // 获取表单数据
    const name = document.getElementById('mappingName').value.trim();
    const description = document.getElementById('mappingDescription').value.trim();
    const sheetName = document.getElementById('sheetName').value.trim();
    const headerRow = parseInt(document.getElementById('headerRow').value);
    const dataStartRow = parseInt(document.getElementById('dataStartRow').value);
    const dateFormat = document.getElementById('dateFormat').value.trim();
    
    // 验证必填字段
    if (!name) {
        showAlert('warning', '请填写配置名称');
        return;
    }
    
    // 获取字段映射
    const fieldRows = document.querySelectorAll('.mapping-field-row');
    if (fieldRows.length === 0) {
        showAlert('warning', '请至少添加一个字段映射');
        return;
    }
    
    const fieldMappings = [];
    for (const row of fieldRows) {
        const systemField = row.querySelector('.system-field').value.trim();
        const excelColumn = row.querySelector('.excel-column').value.trim();
        
        if (!systemField || !excelColumn) {
            showAlert('warning', '字段映射不能为空');
            return;
        }
        
        fieldMappings.push({
            system_field: systemField,
            excel_column: excelColumn
        });
    }
    
    // 从系统字段中提取关键字段（假设第一个字段是关键字段）
    const keyFields = fieldMappings.length > 0 ? fieldMappings[0].system_field : '';
    
    // 构建映射配置
    const newMapping = {
        id: Date.now(), // 使用时间戳作为ID
        name,
        description,
        sheet_name: sheetName,
        header_row: headerRow,
        start_row: dataStartRow,
        key_fields: keyFields,
        field_mappings: fieldMappings,
        date_format: dateFormat
    };
    
    // 禁用保存按钮，显示处理中状态
    const saveBtn = document.getElementById('saveMappingBtn');
    const originalBtnText = saveBtn.innerHTML;
    saveBtn.disabled = true;
    saveBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 保存中...';
    
    // 尝试先保存到API，如果失败再保存到本地
    fetch('/api/excel_mappings/create', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            name,
            description,
            sheet_name: sheetName,
            header_row: headerRow,
            data_start_row: dataStartRow,
            date_format: dateFormat,
            key_fields: keyFields,
            field_mappings: fieldMappings
        })
    })
    .then(response => response.json())
    .then(data => {
        // 恢复按钮状态
        saveBtn.disabled = false;
        saveBtn.innerHTML = originalBtnText;
        
        if (data.status === 'success') {
            // API保存成功，使用返回的数据（包含正确的ID）
            excelMappings.push(data.data);
            
            // 关闭模态框
            bootstrap.Modal.getInstance(document.getElementById('addMappingModal')).hide();
            
            // 显示成功消息
            showAlert('success', '创建Excel映射配置成功');
            
            // 选中新创建的配置
            populateExcelMappingSelect();
            document.getElementById('excelMappingSelect').value = data.data.id;
            updateConfigButtonsState();
        } else {
            console.warn('API保存失败，将保存到本地存储:', data.message);
            showAlert('warning', '无法保存到服务器，将使用本地存储: ' + data.message);
            
            // 添加到配置列表
            excelMappings.push(newMapping);
            
            // 保存到本地存储
            saveExcelMappingsToStorage();
            
            // 更新选择框
            populateExcelMappingSelect();
            
            // 关闭模态框
            bootstrap.Modal.getInstance(document.getElementById('addMappingModal')).hide();
            
            // 选中新创建的配置
            document.getElementById('excelMappingSelect').value = newMapping.id;
            updateConfigButtonsState();
        }
    })
    .catch(error => {
        // 恢复按钮状态
        saveBtn.disabled = false;
        saveBtn.innerHTML = originalBtnText;
        
        console.error('创建Excel映射配置请求出错，将保存到本地存储:', error);
        showAlert('warning', '无法连接到服务器，将使用本地存储');
        
        // 添加到配置列表
        excelMappings.push(newMapping);
        
        // 保存到本地存储
        saveExcelMappingsToStorage();
        
        // 更新选择框
        populateExcelMappingSelect();
        
        // 关闭模态框
        bootstrap.Modal.getInstance(document.getElementById('addMappingModal')).hide();
        
        // 选中新创建的配置
        document.getElementById('excelMappingSelect').value = newMapping.id;
        updateConfigButtonsState();
    });
}

/**
 * Excel映射配置相关功能
 */
document.addEventListener('DOMContentLoaded', function() {
    // 初始化Excel映射配置
    initExcelMappings();
    
    // 绑定新增配置按钮事件
    document.getElementById('addMappingBtn').addEventListener('click', function() {
        showAddMappingModal();
    });
    
    // 绑定添加字段映射按钮事件
    document.getElementById('addFieldBtn').addEventListener('click', function() {
        addMappingField();
    });
    
    // 绑定保存配置按钮事件
    document.getElementById('saveMappingBtn').addEventListener('click', function() {
        saveMappingConfig();
    });
    
    // 绑定编辑配置按钮事件
    document.getElementById('editMappingBtn').addEventListener('click', function() {
        editMappingConfig();
    });
    
    // 绑定删除配置按钮事件
    document.getElementById('deleteMappingBtn').addEventListener('click', function() {
        deleteMappingConfig();
    });
    
    // 绑定批量处理按钮事件
    document.getElementById('batchProcessBtn').addEventListener('click', function() {
        batchProcessAttachments();
    });
});

/**
 * 编辑映射配置
 */
function editMappingConfig() {
    const select = document.getElementById('excelMappingSelect');
    const mappingId = select.value;
    
    if (!mappingId) {
        showAlert('warning', '请先选择要编辑的Excel映射配置');
        return;
    }
    
    // 查找选中的配置
    const mapping = excelMappings.find(m => m.id == mappingId);
    if (!mapping) {
        showAlert('error', '找不到选中的映射配置');
        return;
    }
    
    // 填充表单
    document.getElementById('mappingName').value = mapping.name || '';
    document.getElementById('mappingDescription').value = mapping.description || '';
    document.getElementById('sheetName').value = mapping.sheet_name || '';
    document.getElementById('headerRow').value = mapping.header_row || 1;
    document.getElementById('dataStartRow').value = mapping.start_row || 2;
    document.getElementById('dateFormat').value = mapping.date_format || 'YYYY-MM-DD';
    
    // 清空字段映射区域
    document.getElementById('mappingFieldsContainer').innerHTML = '';
    
    // 添加字段映射
    if (mapping.field_mappings && mapping.field_mappings.length > 0) {
        for (const field of mapping.field_mappings) {
            addMappingField(field.system_field, field.excel_field);
        }
    } else {
        // 添加默认字段
        addMappingField('order_number', '订单号');
    }
    
    // 更改保存按钮行为，添加数据属性用于标识编辑模式
    const saveBtn = document.getElementById('saveMappingBtn');
    saveBtn.dataset.mode = 'edit';
    saveBtn.dataset.mappingId = mappingId;
    
    // 更改模态框标题
    document.getElementById('addMappingModalLabel').textContent = '编辑Excel映射配置';
    
    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('addMappingModal'));
    modal.show();
    
    // 添加一次性的事件监听器用于保存编辑后的配置
    saveBtn.addEventListener('click', saveEditedMapping, { once: true });
}

/**
 * 保存编辑后的映射配置
 */
function saveEditedMapping() {
    const saveBtn = document.getElementById('saveMappingBtn');
    const mappingId = saveBtn.dataset.mappingId;
    
    // 查找配置索引
    const mappingIndex = excelMappings.findIndex(m => m.id == mappingId);
    if (mappingIndex === -1) {
        showAlert('error', '找不到要编辑的配置');
        return;
    }
    
    // 获取表单数据，与保存新配置类似
    const name = document.getElementById('mappingName').value.trim();
    const description = document.getElementById('mappingDescription').value.trim();
    const sheetName = document.getElementById('sheetName').value.trim();
    const headerRow = parseInt(document.getElementById('headerRow').value);
    const dataStartRow = parseInt(document.getElementById('dataStartRow').value);
    const dateFormat = document.getElementById('dateFormat').value.trim();
    
    // 验证必填字段
    if (!name) {
        showAlert('warning', '请填写配置名称');
        return;
    }
    
    // 获取字段映射
    const fieldRows = document.querySelectorAll('.mapping-field-row');
    if (fieldRows.length === 0) {
        showAlert('warning', '请至少添加一个字段映射');
        return;
    }
    
    const fieldMappings = [];
    for (const row of fieldRows) {
        const systemField = row.querySelector('.system-field').value.trim();
        const excelField = row.querySelector('.excel-column').value.trim();
        
        if (!systemField || !excelField) {
            showAlert('warning', '字段映射不能为空');
            return;
        }
        
        fieldMappings.push({
            system_field: systemField,
            excel_field: excelField
        });
    }
    
    // 从系统字段中提取关键字段（假设第一个字段是关键字段）
    const keyFields = fieldMappings.length > 0 ? fieldMappings[0].system_field : '';
    
    // 更新配置
    excelMappings[mappingIndex] = {
        ...excelMappings[mappingIndex], // 保留原有属性
        name,
        description,
        sheet_name: sheetName,
        header_row: headerRow,
        start_row: dataStartRow,
        key_fields: keyFields,
        field_mappings: fieldMappings,
        date_format: dateFormat
    };
    
    // 保存到本地存储
    saveExcelMappingsToStorage();
    
    try {
        // 尝试更新到API
        const requestData = excelMappings[mappingIndex];
        fetch(`/api/excel_mappings/${mappingId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        }).then(response => response.json())
          .then(data => {
              if (data.status === 'success') {
                  console.log('成功更新到API');
              } else {
                  console.warn('API更新失败，但本地存储已更新:', data.message);
              }
          })
          .catch(error => {
              console.error('API更新请求出错，但本地存储已更新:', error);
          });
    } catch (error) {
        console.error('尝试API更新时出错:', error);
    }
    
    // 更新选择框
    populateExcelMappingSelect();
    
    // 关闭模态框
    bootstrap.Modal.getInstance(document.getElementById('addMappingModal')).hide();
    
    // 显示成功消息
    showAlert('success', '更新Excel映射配置成功');
    
    // 选中编辑后的配置
    document.getElementById('excelMappingSelect').value = mappingId;
    updateConfigButtonsState();
    
    // 恢复模态框标题
    document.getElementById('addMappingModalLabel').textContent = '新增Excel映射配置';
    
    // 清除数据属性
    saveBtn.removeAttribute('data-mode');
    saveBtn.removeAttribute('data-mapping-id');
}

/**
 * 删除映射配置
 */
function deleteMappingConfig() {
    const select = document.getElementById('excelMappingSelect');
    const mappingId = select.value;
    
    if (!mappingId) {
        showAlert('warning', '请先选择要删除的Excel映射配置');
        return;
    }
    
    // 确认删除
    if (!confirm('确定要删除此Excel映射配置吗？此操作无法撤销。')) {
        return;
    }
    
    // 查找配置索引
    const mappingIndex = excelMappings.findIndex(m => m.id == mappingId);
    if (mappingIndex === -1) {
        showAlert('error', '找不到要删除的配置');
        return;
    }
    
    // 删除配置
    excelMappings.splice(mappingIndex, 1);
    
    // 保存到本地存储
    saveExcelMappingsToStorage();
    
    try {
        // 尝试从API删除
        fetch(`/api/excel_mappings/${mappingId}`, {
            method: 'DELETE'
        }).then(response => response.json())
          .then(data => {
              if (data.status === 'success') {
                  console.log('成功从API删除');
              } else {
                  console.warn('API删除失败，但本地存储已更新:', data.message);
              }
          })
          .catch(error => {
              console.error('API删除请求出错，但本地存储已更新:', error);
          });
    } catch (error) {
        console.error('尝试API删除时出错:', error);
    }
    
    // 更新选择框
    populateExcelMappingSelect();
    
    // 显示成功消息
    showAlert('success', '删除Excel映射配置成功');
}

/**
 * 批量处理附件
 */
function batchProcessAttachments() {
    const select = document.getElementById('excelMappingSelect');
    const mappingId = select.value;
    
    if (!mappingId) {
        showAlert('warning', '请先选择Excel映射配置');
        return;
    }
    
    // 查找选中的配置
    const mapping = excelMappings.find(m => m.id == mappingId);
    if (!mapping) {
        showAlert('error', '找不到选中的映射配置');
        return;
    }
    
    // 确认操作
    if (!confirm(`确定要使用映射配置"${mapping.name}"批量处理所有未处理的附件吗？`)) {
        return;
    }
    
    // 显示加载中状态
    showAlert('info', '正在批量处理附件，请稍候...', false);
    
    // 禁用批量处理按钮
    const batchBtn = document.getElementById('batchProcessBtn');
    const originalBtnText = batchBtn.innerHTML;
    batchBtn.disabled = true;
    batchBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 处理中...';
    
    // 发送请求到API
    fetch('/api/email_attachments/batch_process', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            mapping_id: mappingId
        })
    })
    .then(response => response.json())
    .then(data => {
        // 恢复按钮状态
        batchBtn.disabled = false;
        batchBtn.innerHTML = originalBtnText;
        
        if (data.status === 'success') {
            // 更新处理结果显示
            document.getElementById('totalFiles').textContent = data.data.total_files;
            document.getElementById('successFiles').textContent = data.data.success_files;
            document.getElementById('errorFiles').textContent = data.data.error_files;
            document.getElementById('totalRecords').textContent = data.data.total_records;
            
            // 更新汇总文件信息
            if (data.data.summary_file) {
                document.getElementById('summaryFilePath').textContent = data.data.summary_file;
                document.getElementById('summaryFileAlert').style.display = 'block';
            } else {
                document.getElementById('summaryFileAlert').style.display = 'none';
            }
            
            // 显示成功消息
            showAlert('success', data.message);
            
            // 刷新附件列表
            if (typeof loadAttachments === 'function') {
                loadAttachments();
            }
        } else {
            showAlert('error', data.message || '批量处理失败');
        }
    })
    .catch(error => {
        // 恢复按钮状态
        batchBtn.disabled = false;
        batchBtn.innerHTML = originalBtnText;
        
        console.error('批量处理附件请求出错:', error);
        showAlert('error', '批量处理附件请求出错: ' + error.message);
    });
}

/**
 * 下载汇总文件
 */
function downloadSummaryFile() {
    // 在实际应用中，这里应该调用API下载汇总文件
    // 现在只显示一个模拟的成功消息
    showAlert('info', '汇总文件下载功能正在开发中');
}

// 为选择框添加change事件
document.addEventListener('DOMContentLoaded', function() {
    const select = document.getElementById('excelMappingSelect');
    if (select) {
        select.addEventListener('change', updateConfigButtonsState);
    }
});
</script>
{% endblock %}
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置调试脚本 - 测试各个配置读取器的结果
用于排查username/user字段问题
"""

import sys
import os
import pymysql

def test_config_readers():
    """测试所有配置读取器"""
    print("=" * 80)
    print("🔍 配置读取器调试测试")
    print("=" * 80)
    
    # 检测运行环境
    is_exe = getattr(sys, 'frozen', False)
    print(f"运行环境: {'EXE打包环境' if is_exe else '开发环境'}")
    
    if is_exe:
        exe_dir = os.path.dirname(sys.executable)
        config_path = os.path.join(exe_dir, 'config.ini')
    else:
        config_path = 'config.ini'
    
    print(f"配置文件路径: {config_path}")
    print(f"配置文件存在: {os.path.exists(config_path)}")
    
    if os.path.exists(config_path):
        print("\n📄 配置文件内容:")
        print("-" * 40)
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                content = f.read()
                print(content)
        except:
            with open(config_path, 'r', encoding='utf-8-sig') as f:
                content = f.read()
                print(content)
        print("-" * 40)
    
    print(f"\n🧪 测试配置读取器:")
    
    # 1. 测试 config_reader
    print("\n1️⃣ 测试 app.utils.config_reader")
    try:
        from app.utils.config_reader import get_database_config
        config1 = get_database_config()
        print(f"   ✅ 成功读取:")
        for key, value in config1.items():
            if key == 'password':
                print(f"      {key}: {'*' * len(str(value))}")
            else:
                print(f"      {key}: {value}")
    except Exception as e:
        print(f"   ❌ 失败: {e}")
    
    # 2. 测试 unified_db_config
    print("\n2️⃣ 测试 app.utils.unified_db_config")
    try:
        from app.utils.unified_db_config import get_unified_db_config
        config2 = get_unified_db_config().get_database_config()
        print(f"   ✅ 成功读取:")
        for key, value in config2.items():
            if key == 'password':
                print(f"      {key}: {'*' * len(str(value))}")
            else:
                print(f"      {key}: {value}")
    except Exception as e:
        print(f"   ❌ 失败: {e}")
    
    # 3. 测试数据库连接
    print("\n3️⃣ 测试实际数据库连接")
    try:
        from app.utils.config_reader import get_database_config
        db_config = get_database_config()
        
        print(f"   🔗 尝试连接: {db_config['user']}@{db_config['host']}:{db_config['port']}")
        
        conn = pymysql.connect(
            host=db_config['host'],
            port=db_config['port'],
            user=db_config['user'],
            password=db_config['password'],
            charset=db_config['charset']
        )
        
        print(f"   ✅ 连接成功!")
        
        # 查询当前用户
        cursor = conn.cursor()
        cursor.execute("SELECT USER(), CURRENT_USER(), CONNECTION_ID()")
        result = cursor.fetchone()
        print(f"   👤 MySQL返回的用户信息:")
        print(f"      USER(): {result[0]}")
        print(f"      CURRENT_USER(): {result[1]}")
        print(f"      CONNECTION_ID(): {result[2]}")
        
        conn.close()
        
    except Exception as e:
        print(f"   ❌ 连接失败: {e}")
        print(f"   📋 错误详情: {type(e).__name__}")
    
    # 4. 测试configparser直接读取
    print("\n4️⃣ 测试 configparser 直接读取")
    try:
        import configparser
        config = configparser.ConfigParser()
        config.read(config_path, encoding='utf-8')
        
        if config.has_section('DATABASE'):
            print("   ✅ 找到DATABASE section:")
            for key in config['DATABASE']:
                value = config['DATABASE'][key]
                if key == 'password':
                    print(f"      {key} = {'*' * len(value)}")
                else:
                    print(f"      {key} = {value}")
                    
            # 测试字段存在性
            print(f"\n   🔍 字段存在性检查:")
            print(f"      'user' 存在: {'user' in config['DATABASE']}")
            print(f"      'username' 存在: {'username' in config['DATABASE']}")
            
            if 'username' in config['DATABASE']:
                username_value = config['DATABASE']['username']
                print(f"      'username' 值: {username_value}")
            
            if 'user' in config['DATABASE']:
                user_value = config['DATABASE']['user']
                print(f"      'user' 值: {user_value}")
        else:
            print("   ❌ 未找到DATABASE section")
            
    except Exception as e:
        print(f"   ❌ 失败: {e}")

if __name__ == '__main__':
    test_config_readers()
    print("\n" + "=" * 80)
    input("按回车键退出...") 
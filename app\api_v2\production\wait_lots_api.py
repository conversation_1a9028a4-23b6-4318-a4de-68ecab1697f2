"""
待排产批次API接口
"""
from flask import Blueprint, jsonify, request
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
from app import db
from app.models.production.wait_lots import WaitLot
from datetime import datetime
import logging
import pandas as pd

logger = logging.getLogger(__name__)

# 创建待排产批次API蓝图
wait_lots_bp = Blueprint('wait_lots_api', __name__, url_prefix='/api/v2/production/wait-lots')

@wait_lots_bp.route('/health')
def health_check():
    """待排产批次模块健康检查"""
    return jsonify({
        'status': 'ok',
        'service': 'wait_lots_api',
        'timestamp': datetime.now().isoformat()
    })

@wait_lots_bp.route('/', methods=['GET'])
@login_required
def get_wait_lots():
    """获取待排产批次数据"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)
        
        # 构建查询
        query = WaitLot.query
        
        # 应用筛选条件
        filters = request.args.get('filters')
        if filters:
            # 这里可以添加筛选逻辑
            pass
        
        # 分页查询
        pagination = query.paginate(
            page=page, 
            per_page=per_page, 
            error_out=False
        )
        
        # 转换数据格式
        data = [item.to_dict() for item in pagination.items]
        
        return jsonify({
            'success': True,
            'data': data,
            'total': pagination.total,
            'pages': pagination.pages,
            'current_page': page,
            'per_page': per_page
        })
        
    except Exception as e:
        logger.error(f"获取待排产批次数据失败: {str(e)}")
        return jsonify({'error': '获取数据失败'}), 500

@wait_lots_bp.route('/<int:lot_id>', methods=['GET'])
@login_required
def get_wait_lot(lot_id):
    """获取单个待排产批次详情"""
    try:
        lot = WaitLot.query.get_or_404(lot_id)
        return jsonify({
            'success': True,
            'data': lot.to_dict()
        })
    except Exception as e:
        logger.error(f"获取待排产批次详情失败: {str(e)}")
        return jsonify({'error': '获取详情失败'}), 500

@wait_lots_bp.route('/', methods=['POST'])
@login_required
def create_wait_lot():
    """创建待排产批次"""
    try:
        data = request.get_json()
        
        # 创建新记录
        lot = WaitLot(
            lot_id=data.get('lot_id'),
            device=data.get('device'),
            stage=data.get('stage'),
            quantity=data.get('quantity'),
            pkg_pn=data.get('pkg_pn'),
            chip_id=data.get('chip_id'),
            priority=data.get('priority'),
            equipment_id=data.get('equipment_id'),
            status=data.get('status', 'WAITING')
        )
        
        db.session.add(lot)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '创建成功',
            'data': lot.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"创建待排产批次失败: {str(e)}")
        return jsonify({'error': '创建失败'}), 500

@wait_lots_bp.route('/<int:lot_id>', methods=['PUT'])
@login_required
def update_wait_lot(lot_id):
    """更新待排产批次"""
    try:
        lot = WaitLot.query.get_or_404(lot_id)
        data = request.get_json()
        
        # 更新字段
        for field in ['lot_id', 'device', 'stage', 'quantity', 'pkg_pn', 
                      'chip_id', 'priority', 'equipment_id', 'status']:
            if field in data:
                setattr(lot, field, data[field])
        
        lot.updated_at = datetime.utcnow()
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '更新成功',
            'data': lot.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"更新待排产批次失败: {str(e)}")
        return jsonify({'error': '更新失败'}), 500

@wait_lots_bp.route('/<int:lot_id>', methods=['DELETE'])
@login_required
def delete_wait_lot(lot_id):
    """删除待排产批次"""
    try:
        lot = WaitLot.query.get_or_404(lot_id)
        db.session.delete(lot)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '删除成功'
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"删除待排产批次失败: {str(e)}")
        return jsonify({'error': '删除失败'}), 500

@wait_lots_bp.route('/columns', methods=['GET'])
@login_required
def get_wait_lot_columns():
    """获取待排产批次表列信息"""
    try:
        columns = [
            {'name': 'id', 'label': 'ID', 'type': 'integer', 'editable': False},
            {'name': 'lot_id', 'label': '内部工单号', 'type': 'string', 'editable': True},
            {'name': 'device', 'label': '产品名称', 'type': 'string', 'editable': True},
            {'name': 'stage', 'label': '工序', 'type': 'string', 'editable': True},
            {'name': 'quantity', 'label': '数量', 'type': 'integer', 'editable': True},
            {'name': 'pkg_pn', 'label': '封装料号', 'type': 'string', 'editable': True},
            {'name': 'chip_id', 'label': '芯片名称', 'type': 'string', 'editable': True},
            {'name': 'priority', 'label': '优先级', 'type': 'integer', 'editable': True},
            {'name': 'equipment_id', 'label': '设备ID', 'type': 'string', 'editable': True},
            {'name': 'status', 'label': '状态', 'type': 'string', 'editable': True},
            {'name': 'created_at', 'label': '创建时间', 'type': 'datetime', 'editable': False},
            {'name': 'updated_at', 'label': '更新时间', 'type': 'datetime', 'editable': False}
        ]
        
        return jsonify({
            'success': True,
            'data': columns
        })
        
    except Exception as e:
        logger.error(f"获取待排产批次列信息失败: {str(e)}")
        return jsonify({'error': '获取列信息失败'}), 500

@wait_lots_bp.route('/move-to-scheduled', methods=['POST'])
@login_required
def move_to_scheduled():
    """将待排产批次移至已排产"""
    try:
        data = request.get_json()
        ids = data.get('ids', [])
        
        if not ids:
            return jsonify({'success': False, 'error': '没有选择要移动的批次'}), 400
        
        # 获取要移动的批次
        lots = WaitLot.query.filter(WaitLot.id.in_(ids)).all()
        if not lots:
            return jsonify({'success': False, 'error': '没有找到要移动的批次'}), 404
        
        from app.models.production.done_lots import DoneLot
        
        moved_count = 0
        for lot in lots:
            # 创建已排产记录
            done_lot = DoneLot(
                lot_id=lot.lot_id,
                device=lot.device,
                stage=lot.stage,
                quantity=lot.quantity,
                pkg_pn=lot.pkg_pn,
                chip_id=lot.chip_id,
                priority=lot.priority,
                equipment_id=lot.equipment_id,
                status='SCHEDULED',
                completion_rate=0.0,
                user=current_user.username,
                scheduled_start_time=datetime.utcnow()
            )
            
            db.session.add(done_lot)
            db.session.delete(lot)
            moved_count += 1
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': f'成功移动 {moved_count} 个批次至已排产',
            'moved_count': moved_count
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"移动批次至已排产失败: {str(e)}")
        return jsonify({'error': '移动失败'}), 500

@wait_lots_bp.route('/upload', methods=['POST'])
@login_required
def upload_wait_lots_excel():
    """上传待排产批次Excel文件"""
    try:
        logger.info(f"用户 {current_user.username} 开始上传待排产批次Excel文件")

        if 'files' not in request.files:
            return jsonify({'success': False, 'error': '没有选择文件'})

        files = request.files.getlist('files')
        if not files or all(f.filename == '' for f in files):
            return jsonify({'success': False, 'error': '没有选择文件'})

        # 限制只能上传一个文件
        if len(files) > 1:
            return jsonify({'success': False, 'error': '只允许上传单个Excel文件'})

        file = files[0]
        if not file or file.filename == '':
            return jsonify({'success': False, 'error': '没有选择文件'})

        filename = secure_filename(file.filename)
        logger.info(f"处理文件: {filename}")

        # 严格验证文件类型
        filename_lower = filename.lower()
        if not (filename_lower.endswith('.xlsx') or filename_lower.endswith('.xls')):
            return jsonify({'success': False, 'error': '只支持Excel文件格式(.xlsx或.xls)'})

        # 验证文件名包含正确的关键字
        if not ('wait' in filename_lower or 'lot' in filename_lower):
            return jsonify({
                'success': False,
                'error': '文件名必须包含"wait"或"lot"关键字，以便系统识别文件类型'
            })

        result = process_wait_lots_excel_file(file, filename)

        return jsonify({
            'success': result['success'],
            'results': [result],
            'total_processed': result.get('imported_count', 0) if result['success'] else 0,
            'message': result.get('error') if not result['success'] else f'成功导入 {result.get("imported_count", 0)} 条记录'
        })

    except Exception as e:
        logger.error(f"上传待排产批次Excel文件失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

def process_wait_lots_excel_file(file, filename):
    """处理待排产批次Excel文件"""
    try:
        logger.info(f"开始处理文件: {filename}")

        # 读取Excel文件
        try:
            df = pd.read_excel(file)
            logger.info(f"成功读取Excel文件，共 {len(df)} 行数据")
        except Exception as e:
            error_msg = f'读取Excel文件失败: {str(e)}'
            logger.error(error_msg)
            return {
                'filename': filename,
                'success': False,
                'error': error_msg
            }

        if df.empty:
            return {
                'filename': filename,
                'success': False,
                'error': 'Excel文件为空'
            }

        # 记录现有数据数量
        existing_count = WaitLot.query.count()
        logger.info(f"表 wait_lots 现有 {existing_count} 条记录")

        imported_count = 0
        errors = []

        # 清空现有数据（可选，根据需求决定）
        # WaitLot.query.delete()

        # 处理每一行数据
        for index, row in df.iterrows():
            try:
                # 检查必需字段
                if pd.isna(row.get('LOT_ID')):
                    errors.append(f'第{index+2}行: LOT_ID字段不能为空')
                    continue

                # 检查是否已存在相同的LOT_ID
                existing_lot = WaitLot.query.filter_by(lot_id=str(row.get('LOT_ID', ''))).first()
                if existing_lot:
                    logger.info(f"批次 {row.get('LOT_ID')} 已存在，跳过")
                    continue

                record = WaitLot(
                    lot_id=str(row.get('LOT_ID', '')),
                    device=str(row.get('DEVICE', '')) if pd.notna(row.get('DEVICE')) else None,
                    stage=str(row.get('STAGE', '')) if pd.notna(row.get('STAGE')) else None,
                    quantity=int(row.get('GOOD_QTY', 0)) if pd.notna(row.get('GOOD_QTY')) else 0,
                    pkg_pn=str(row.get('PKG_PN', '')) if pd.notna(row.get('PKG_PN')) else None,
                    chip_id=str(row.get('CHIP_ID', '')) if pd.notna(row.get('CHIP_ID')) else None,
                    priority=int(row.get('PRIORITY', 5)) if pd.notna(row.get('PRIORITY')) else 5,
                    status='WAITING'
                )

                db.session.add(record)
                imported_count += 1

            except Exception as e:
                error_msg = f'第{index+2}行数据处理失败: {str(e)}'
                errors.append(error_msg)
                logger.warning(error_msg)
                continue

        # 提交事务
        try:
            db.session.commit()
            logger.info(f"成功导入 {imported_count} 条待排产批次记录")
        except Exception as e:
            db.session.rollback()
            error_msg = f'数据库提交失败: {str(e)}'
            logger.error(error_msg)
            return {
                'filename': filename,
                'success': False,
                'error': error_msg
            }

        # 返回结果
        result = {
            'filename': filename,
            'success': True,
            'imported_count': imported_count,
            'total_rows': len(df),
            'existing_count': existing_count
        }

        if errors:
            result['warnings'] = errors
            result['warning_count'] = len(errors)

        return result

    except Exception as e:
        db.session.rollback()
        error_msg = f'处理文件失败: {str(e)}'
        logger.error(error_msg)
        return {
            'filename': filename,
            'success': False,
            'error': error_msg
        }
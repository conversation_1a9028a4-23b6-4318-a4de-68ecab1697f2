#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量迁移数据库连接到连接池的脚本
"""

import os
import re
import shutil
from datetime import datetime

def backup_file(file_path):
    """备份文件"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"{file_path}.backup_{timestamp}"
    shutil.copy2(file_path, backup_path)
    print(f"✅ 已备份: {backup_path}")
    return backup_path

def migrate_file(file_path):
    """迁移单个文件"""
    print(f"\n🔄 处理文件: {file_path}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 1. 添加连接池导入（如果不存在）
        if 'from app.utils.db_connection_pool import' not in content:
            # 查找现有的pymysql导入位置
            import_pattern = r'import pymysql'
            if re.search(import_pattern, content):
                content = re.sub(
                    import_pattern,
                    'import pymysql\nfrom app.utils.db_connection_pool import get_db_connection_context, get_db_connection',
                    content,
                    count=1
                )
            else:
                # 在文件开头添加导入
                lines = content.split('\n')
                insert_pos = 0
                for i, line in enumerate(lines):
                    if line.startswith('import ') or line.startswith('from '):
                        insert_pos = i + 1
                    elif line.strip() and not line.startswith('#') and not line.startswith('"""') and not line.startswith("'''"):
                        break
                
                lines.insert(insert_pos, 'from app.utils.db_connection_pool import get_db_connection_context, get_db_connection')
                content = '\n'.join(lines)
        
        # 2. 替换 pymysql.connect(**config) 模式
        pymysql_patterns = [
            # 直接连接模式
            (r'connection = pymysql\.connect\(\*\*([^)]+)\)\s*\n\s*with connection\.cursor\(\) as cursor:',
             r'with get_db_connection_context() as connection:\n                cursor = connection.cursor()'),
            
            # 简单连接模式
            (r'connection = pymysql\.connect\(\*\*([^)]+)\)',
             r'connection = get_db_connection()  # 使用连接池'),
            
            # 带参数的连接
            (r'conn = pymysql\.connect\(\*\*([^)]+)\)',
             r'conn = get_db_connection()  # 使用连接池'),
        ]
        
        for pattern, replacement in pymysql_patterns:
            content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
        
        # 3. 替换 get_mysql_connection() 调用为连接池
        get_mysql_patterns = [
            # 基本调用
            (r'conn = get_mysql_connection\(\)',
             r'conn = get_db_connection()'),
            
            # 带参数调用  
            (r'conn = get_mysql_connection\(([^)]*)\)',
             r'conn = get_db_connection()  # 原参数: \1'),
            
            # connection变量
            (r'connection = get_mysql_connection\(\)',
             r'connection = get_db_connection()'),
            
            # 带参数的connection
            (r'connection = get_mysql_connection\(([^)]*)\)',
             r'connection = get_db_connection()  # 原参数: \1'),
        ]
        
        for pattern, replacement in get_mysql_patterns:
            content = re.sub(pattern, replacement, content)
        
        # 4. 更新配置读取方式
        config_patterns = [
            # 旧的配置导入
            (r'from app\.utils\.config_reader import get_database_config',
             r'# 已迁移到连接池，不再需要直接读取配置\n# from app.utils.config_reader import get_database_config'),
        ]
        
        for pattern, replacement in config_patterns:
            content = re.sub(pattern, replacement, content)
        
        # 5. 移除不必要的连接关闭
        close_patterns = [
            (r'\s*connection\.close\(\)\s*\n', '\n'),
            (r'\s*conn\.close\(\)\s*\n', '\n'),
        ]
        
        for pattern, replacement in close_patterns:
            content = re.sub(pattern, replacement, content)
        
        # 检查是否有变化
        if content != original_content:
            # 备份原文件
            backup_file(file_path)
            
            # 写入更新后的内容
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ 已更新: {file_path}")
            return True
        else:
            print(f"⚪ 无需更新: {file_path}")
            return False
            
    except Exception as e:
        print(f"❌ 处理失败 {file_path}: {e}")
        return False

def main():
    """主函数"""
    print("🚀 批量迁移数据库连接到连接池")
    print("=" * 50)
    
    # 需要迁移的文件列表（根据扫描结果）
    files_to_migrate = [
        # 优先处理核心文件
        'app/utils/database_safety_checker.py',
        'app/utils/high_performance_email_processor.py', 
        'app/utils/scheduling_lock_manager.py',
        'app/api_v2/wip_lot_api.py',
        'app/api_v2/production/done_lots_api.py',
        'app/api_v2/production/routes.py',
        'app/api_v2/system/routes.py',
        'app/routes/production_views.py',
        
        # Tools文件
        'tools/database/clear_tables_data.py',
        'tools/database/init_db.py',
        'tools/data_import/import_excel_to_mysql.py',
        'tools/data_import/import_excel_to_mysql_safe.py',
        'tools/data_import/table_structure_validator.py',
        'tools/monitoring/scheduling_failure_fix.py',
        
        # Migration scripts
        'migration_scripts/data_migrator.py',
        'migration_scripts/migration_validator.py',
        'migration_scripts/rollback_manager.py',
    ]
    
    success_count = 0
    total_count = len(files_to_migrate)
    
    for file_path in files_to_migrate:
        if os.path.exists(file_path):
            if migrate_file(file_path):
                success_count += 1
        else:
            print(f"⚠️ 文件不存在: {file_path}")
    
    print(f"\n📊 迁移完成:")
    print(f"  成功迁移: {success_count}/{total_count}")
    print(f"  成功率: {success_count/total_count*100:.1f}%")
    
    if success_count > 0:
        print(f"\n✅ 建议下一步:")
        print(f"  1. 运行: python test_connection_pool.py")
        print(f"  2. 测试应用启动: python run.py")
        print(f"  3. 检查迁移状态: python check_connection_pool_migration.py")

if __name__ == "__main__":
    main() 
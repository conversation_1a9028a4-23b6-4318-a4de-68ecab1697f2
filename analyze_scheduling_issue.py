#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
排产问题分析脚本
专门分析LYW6119Ea5QFND-SDA1_TR批次为什么没有被安排到HCHC-C-030-6800设备上
模拟real_scheduling_service.py的完整排产逻辑
"""

import pymysql
import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SchedulingAnalyzer:
    def __init__(self):
        self.connection = pymysql.connect(
            host='localhost',
            user='root',
            password='WWWwww123!',
            database='aps',
            charset='utf8mb4'
        )
        
    def analyze_scheduling_issue(self):
        """分析LYW6119Ea5QFND-SDA1_TR批次的排产问题"""
        print("🔍 开始分析LYW6119Ea5QFND-SDA1_TR批次排产问题")
        print("=" * 80)
        
        # 1. 获取待排产批次信息
        lot_info = self._get_lot_info()
        if not lot_info:
            print("❌ 未找到批次信息，分析结束")
            return
            
        print(f"📋 批次信息:")
        print(f"  LOT_ID: {lot_info.get('LOT_ID', 'N/A')}")
        print(f"  DEVICE: {lot_info.get('DEVICE', 'N/A')}")
        print(f"  STAGE: {lot_info.get('STAGE', 'N/A')}")
        print(f"  PKG_PN: {lot_info.get('PKG_PN', 'N/A')}")
        print(f"  GOOD_QTY: {lot_info.get('GOOD_QTY', 'N/A')}")
        print()
        
        # 2. 获取目标设备信息
        equipment_info = self._get_equipment_info('HCHC-C-030-6800')
        if not equipment_info:
            print("❌ 未找到设备信息，分析结束")
            return
            
        print(f"⚙️ 目标设备信息:")
        print(f"  HANDLER_ID: {equipment_info.get('HANDLER_ID', 'N/A')}")
        print(f"  DEVICE: {equipment_info.get('DEVICE', 'N/A')}")
        print(f"  STAGE: {equipment_info.get('STAGE', 'N/A')}")
        print(f"  STATUS: {equipment_info.get('STATUS', 'N/A')}")
        print(f"  EQP_CLASS: {equipment_info.get('EQP_CLASS', 'N/A')}")
        print(f"  HANDLER_CONFIG: {equipment_info.get('HANDLER_CONFIG', 'N/A')}")
        print()
        
        # 3. 模拟排产服务的配置需求获取过程
        print("🔧 步骤1: 模拟配置需求获取过程...")
        config_requirements = self._simulate_get_configuration_requirements(lot_info)
        
        if not config_requirements:
            print("❌ 配置需求获取失败 - 这是排产失败的第一个原因")
            self._analyze_configuration_failure(lot_info)
            return
        else:
            print("✅ 配置需求获取成功")
            print(f"  配置来源: {config_requirements.get('TEST_SPEC_SOURCE', 'UNKNOWN')}")
            print()
        
        # 4. 模拟设备匹配评分过程
        print("⚙️ 步骤2: 模拟设备匹配评分过程...")
        match_score, match_type, changeover_time = self._simulate_equipment_matching(
            config_requirements, equipment_info
        )
        
        print(f"  匹配评分: {match_score}")
        print(f"  匹配类型: {match_type}")
        print(f"  改机时间: {changeover_time}分钟")
        print()
        
        # 5. 分析匹配失败的具体原因
        if match_score == 0:
            print("❌ 设备匹配失败 - 这是排产失败的主要原因")
            self._analyze_matching_failure(config_requirements, equipment_info)
        else:
            print("✅ 设备匹配成功")
            
        # 6. 检查设备状态
        print("🔍 步骤3: 检查设备可用性...")
        if equipment_info.get('STATUS') == 'DOWN':
            print("❌ 设备状态为DOWN - 这是排产失败的另一个原因")
        else:
            print("✅ 设备状态正常")
        print()
        
        # 7. 查找历史成功记录
        print("📊 步骤4: 查找历史成功记录...")
        historical_records = self._get_historical_records(lot_info, equipment_info)
        if historical_records:
            print(f"✅ 找到{len(historical_records)}条历史成功记录")
            for record in historical_records[:3]:  # 显示前3条
                print(f"  {record['LOT_ID']} | {record['STAGE']} | 良率:{record['FINAL_YIELD']}")
        else:
            print("⚠️ 未找到历史成功记录")
        print()
        
        # 8. 总结分析结果
        self._summarize_analysis(lot_info, equipment_info, config_requirements, 
                                match_score, historical_records)
    
    def _get_lot_info(self) -> Optional[Dict]:
        """获取批次信息"""
        cursor = self.connection.cursor(pymysql.cursors.DictCursor)
        
        # 查找相似的批次（因为具体的LOT_ID可能不存在）
        cursor.execute("""
            SELECT LOT_ID, DEVICE, STAGE, PKG_PN, CHIP_ID, GOOD_QTY, CREATE_TIME
            FROM et_wait_lot 
            WHERE DEVICE LIKE %s AND STAGE = %s
            ORDER BY CREATE_TIME DESC
            LIMIT 1
        """, ('%LYW6119Ea5QFND%', 'LSTR'))
        
        return cursor.fetchone()
    
    def _get_equipment_info(self, handler_id: str) -> Optional[Dict]:
        """获取设备信息"""
        cursor = self.connection.cursor(pymysql.cursors.DictCursor)
        
        cursor.execute("""
            SELECT HANDLER_ID, DEVICE, STAGE, STATUS, EQP_CLASS, HANDLER_TYPE, 
                   HANDLER_CONFIG, KIT_PN, HB_PN, TB_PN
            FROM eqp_status 
            WHERE HANDLER_ID = %s
        """, (handler_id,))
        
        return cursor.fetchone()
    
    def _simulate_get_configuration_requirements(self, lot_info: Dict) -> Optional[Dict]:
        """模拟配置需求获取过程"""
        device = lot_info.get('DEVICE', '')
        stage = lot_info.get('STAGE', '')
        pkg_pn = lot_info.get('PKG_PN', '')
        
        # 1. 检查特殊阶段处理
        if 'LSTR' in stage.upper():
            print("  🔍 检测到LSTR特殊阶段")
            # 检查是否有LSTR类型设备
            cursor = self.connection.cursor()
            cursor.execute("""
                SELECT COUNT(*) FROM eqp_status 
                WHERE EQP_CLASS = 'LSTR' AND STATUS != 'DOWN'
            """)
            lstr_equipment_count = cursor.fetchone()[0]
            
            if lstr_equipment_count == 0:
                print("  ❌ 没有可用的LSTR类型设备")
                return None
            else:
                print(f"  ✅ 找到{lstr_equipment_count}台LSTR设备")
                return {
                    'DEVICE': device,
                    'STAGE': stage,
                    'PKG_PN': pkg_pn,
                    'HANDLER_CONFIG': 'LSTR_REQUIRED',
                    'EQP_CLASS': 'LSTR',
                    'TEST_SPEC_SOURCE': 'LSTR_SPECIAL_STAGE'
                }
        
        # 2. 查找标准测试规范
        print("  🔍 查找标准测试规范...")
        cursor = self.connection.cursor(pymysql.cursors.DictCursor)
        cursor.execute("""
            SELECT DEVICE, STAGE, PKG_PN, TB_PN, HB_PN, HANDLER, APPROVAL_STATE
            FROM et_ft_test_spec 
            WHERE DEVICE = %s AND STAGE = %s AND APPROVAL_STATE = 'Released'
            LIMIT 1
        """, (device, stage))
        
        test_spec = cursor.fetchone()
        if test_spec:
            print("  ✅ 找到标准测试规范")
            return {
                'DEVICE': device,
                'STAGE': stage,
                'PKG_PN': pkg_pn,
                'TB_PN': test_spec.get('TB_PN', ''),
                'HB_PN': test_spec.get('HB_PN', ''),
                'HANDLER_CONFIG': test_spec.get('HANDLER', ''),
                'TEST_SPEC_SOURCE': 'STANDARD'
            }
        
        # 3. 尝试CT历史数据
        print("  🔍 查找CT历史数据...")
        cursor.execute("""
            SELECT DEVICE, STAGE, PKG_PN, MAIN_EQP_ID, AUXILIARY_EQP_ID
            FROM ct 
            WHERE DEVICE = %s AND STAGE = %s
            ORDER BY CREATE_TIME DESC
            LIMIT 1
        """, (device, stage))
        
        ct_record = cursor.fetchone()
        if ct_record:
            print("  ✅ 找到CT历史数据")
            return {
                'DEVICE': device,
                'STAGE': stage,
                'PKG_PN': pkg_pn,
                'HANDLER_ID': ct_record.get('AUXILIARY_EQP_ID', ''),
                'TEST_SPEC_SOURCE': 'CT_HISTORICAL'
            }
        
        print("  ❌ 未找到任何配置需求")
        return None
    
    def _simulate_equipment_matching(self, config_requirements: Dict, 
                                   equipment_info: Dict) -> Tuple[int, str, int]:
        """模拟设备匹配评分"""
        req_device = config_requirements.get('DEVICE', '')
        req_stage = config_requirements.get('STAGE', '')
        req_handler_config = config_requirements.get('HANDLER_CONFIG', '')
        
        eqp_device = equipment_info.get('DEVICE', '')
        eqp_stage = equipment_info.get('STAGE', '')
        eqp_handler_config = equipment_info.get('HANDLER_CONFIG', '')
        eqp_class = equipment_info.get('EQP_CLASS', '')
        
        # 1. 检查特殊阶段匹配
        if 'LSTR' in req_stage:
            if eqp_class != 'LSTR':
                return (0, "LSTR阶段需要LSTR类型设备", 9999)
        
        # 2. 检查产品匹配
        if req_device != eqp_device:
            return (0, "产品不匹配", 9999)
        
        # 3. 检查工序匹配
        if req_stage != eqp_stage:
            return (0, "工序不匹配", 9999)
        
        # 4. 检查配置匹配
        if req_handler_config and eqp_handler_config:
            if req_handler_config != eqp_handler_config:
                return (60, "大改机匹配", 120)
        
        return (100, "完全匹配", 0)
    
    def _analyze_configuration_failure(self, lot_info: Dict):
        """分析配置需求获取失败的原因"""
        print("\n🔍 配置需求获取失败原因分析:")
        
        device = lot_info.get('DEVICE', '')
        stage = lot_info.get('STAGE', '')
        
        # 1. 检查LSTR特殊阶段处理
        if 'LSTR' in stage:
            cursor = self.connection.cursor()
            cursor.execute("""
                SELECT HANDLER_ID, STATUS, EQP_CLASS 
                FROM eqp_status 
                WHERE EQP_CLASS = 'LSTR'
            """)
            lstr_equipment = cursor.fetchall()
            
            if not lstr_equipment:
                print("  ❌ 根本原因: EQP_STATUS表中没有EQP_CLASS='LSTR'的设备")
                print("  💡 解决方案: 需要在EQP_STATUS表中添加LSTR类型的设备")
            else:
                print(f"  ✅ 找到{len(lstr_equipment)}台LSTR设备:")
                for eqp in lstr_equipment:
                    print(f"    {eqp[0]} | {eqp[1]} | {eqp[2]}")
        
        # 2. 检查测试规范
        cursor = self.connection.cursor()
        cursor.execute("""
            SELECT COUNT(*) FROM et_ft_test_spec 
            WHERE DEVICE = %s AND STAGE = %s
        """, (device, stage))
        spec_count = cursor.fetchone()[0]
        
        if spec_count == 0:
            print(f"  ❌ ET_FT_TEST_SPEC表中没有{device}产品{stage}工序的测试规范")
            print("  💡 解决方案: 需要添加对应的测试规范")
        else:
            print(f"  ✅ 找到{spec_count}条测试规范")
    
    def _analyze_matching_failure(self, config_requirements: Dict, equipment_info: Dict):
        """分析设备匹配失败的原因"""
        print("\n🔍 设备匹配失败原因分析:")
        
        req_device = config_requirements.get('DEVICE', '')
        req_stage = config_requirements.get('STAGE', '')
        
        eqp_device = equipment_info.get('DEVICE', '')
        eqp_stage = equipment_info.get('STAGE', '')
        eqp_class = equipment_info.get('EQP_CLASS', '')
        
        if req_device != eqp_device:
            print(f"  ❌ 产品不匹配: 需要{req_device}, 设备当前配置{eqp_device}")
            
        if req_stage != eqp_stage:
            print(f"  ❌ 工序不匹配: 需要{req_stage}, 设备当前工序{eqp_stage}")
            
        if 'LSTR' in req_stage and eqp_class != 'LSTR':
            print(f"  ❌ 设备类型不匹配: LSTR工序需要LSTR类型设备, 当前设备类型{eqp_class}")
    
    def _get_historical_records(self, lot_info: Dict, equipment_info: Dict) -> List[Dict]:
        """获取历史成功记录"""
        cursor = self.connection.cursor(pymysql.cursors.DictCursor)
        
        device = lot_info.get('DEVICE', '')
        handler_id = equipment_info.get('HANDLER_ID', '')
        
        cursor.execute("""
            SELECT LOT_ID, DEVICE, STAGE, FINAL_YIELD, CREATE_TIME
            FROM ct 
            WHERE DEVICE LIKE %s AND (MAIN_EQP_ID = %s OR AUXILIARY_EQP_ID = %s)
            ORDER BY CREATE_TIME DESC
            LIMIT 5
        """, (f'%{device.split("-")[0]}%', handler_id, handler_id))
        
        return cursor.fetchall()
    
    def _summarize_analysis(self, lot_info: Dict, equipment_info: Dict, 
                          config_requirements: Optional[Dict], match_score: int,
                          historical_records: List[Dict]):
        """总结分析结果"""
        print("📊 分析结果总结:")
        print("=" * 60)
        
        device = lot_info.get('DEVICE', '')
        stage = lot_info.get('STAGE', '')
        handler_id = equipment_info.get('HANDLER_ID', '')
        
        print(f"批次 {device} (工序:{stage}) 无法安排到设备 {handler_id} 的原因:")
        print()
        
        # 主要原因分析
        reasons = []
        
        if not config_requirements:
            reasons.append("1. ❌ 配置需求获取失败")
            if 'LSTR' in stage:
                reasons.append("   - LSTR特殊阶段需要LSTR类型设备，但系统中没有")
            else:
                reasons.append("   - 没有找到对应的测试规范")
        
        if equipment_info.get('STATUS') == 'DOWN':
            reasons.append("2. ❌ 设备状态为DOWN，不可用")
        
        if config_requirements and match_score == 0:
            reasons.append("3. ❌ 设备匹配失败")
            if equipment_info.get('STAGE') != stage:
                reasons.append(f"   - 工序不匹配: 设备当前工序({equipment_info.get('STAGE')}) != 需要工序({stage})")
            if 'LSTR' in stage and equipment_info.get('EQP_CLASS') != 'LSTR':
                reasons.append(f"   - 设备类型不匹配: LSTR工序需要LSTR类型设备")
        
        if not reasons:
            reasons.append("✅ 技术上可以匹配，可能是其他因素影响")
        
        for reason in reasons:
            print(reason)
        
        print()
        print("💡 解决方案建议:")
        
        if 'LSTR' in stage:
            print("1. 在EQP_STATUS表中添加EQP_CLASS='LSTR'的设备记录")
            print("2. 或者修改现有设备的EQP_CLASS为'LSTR'以支持LSTR工序")
        
        if equipment_info.get('STATUS') == 'DOWN':
            print("3. 修复设备状态，将STATUS从'DOWN'改为'IDLE'或'Run'")
        
        if historical_records:
            print("4. 历史记录显示该产品可以在此设备上运行，考虑放宽匹配规则")
        
        print()
        print("🔍 历史验证:")
        if historical_records:
            print(f"✅ 该产品族在{handler_id}设备上有{len(historical_records)}次成功运行记录")
            avg_yield = sum(float(r.get('FINAL_YIELD', 0)) for r in historical_records) / len(historical_records)
            print(f"   平均良率: {avg_yield:.2%}")
        else:
            print(f"⚠️ 该产品族在{handler_id}设备上没有历史运行记录")
    
    def __del__(self):
        if hasattr(self, 'connection') and self.connection:
            self.connection.close()

if __name__ == "__main__":
    analyzer = SchedulingAnalyzer()
    analyzer.analyze_scheduling_issue() 
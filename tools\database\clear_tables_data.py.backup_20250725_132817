#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
APS 数据库表数据清空工具
清空指定的数据表中的所有数据，保留表结构
包含基础数据表和页面相关数据表
"""

import os
import sys
import logging
import pymysql
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/clear_tables.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('APS-Clear')

def get_mysql_connection(database='aps'):
    """获取MySQL数据库连接 - 使用统一配置管理器"""
    try:
        # 尝试使用统一配置管理器
        try:
            sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
            from app.utils.unified_db_config import get_pymysql_config
            config = get_pymysql_config(database)
            logger.info(f"✅ 使用config.ini配置: {config['host']}:{config['port']}")
        except Exception as e:
            logger.warning(f"配置文件读取失败: {e}")
            if getattr(sys, 'frozen', False):
                raise Exception("❌ exe环境必须提供config.ini配置文件！")
            # 开发环境降级
            config = {
                'host': 'localhost',
                'port': 3306,
                'user': 'root',
                'password': 'WWWwww123!',
                'database': database,
                'charset': 'utf8mb4',
                'autocommit': True
            }
            logger.info("使用开发环境默认配置")
        
        connection = pymysql.connect(**config)
        logger.info(f"✅ MySQL连接成功: {config['host']}:{config['port']}/{database}")
        return connection
        
    except Exception as e:
        logger.error(f"❌ MySQL连接失败: {e}")
        raise

def check_table_exists(cursor, table_name):
    """检查表是否存在"""
    cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
    return cursor.fetchone() is not None

def get_table_row_count(cursor, table_name):
    """获取表的记录数"""
    try:
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        return cursor.fetchone()[0]
    except Exception:
        return 0

def backup_table_data(cursor, table_name):
    """备份表数据（可选）"""
    try:
        backup_table_name = f"{table_name}_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        cursor.execute(f"CREATE TABLE {backup_table_name} AS SELECT * FROM {table_name}")
        logger.info(f"✅ 表 {table_name} 数据已备份到 {backup_table_name}")
        return backup_table_name
    except Exception as e:
        logger.warning(f"⚠️ 备份表 {table_name} 失败: {e}")
        return None

def clear_table_data(cursor, table_name):
    """清空表数据"""
    try:
        # 使用TRUNCATE TABLE清空数据（比DELETE FROM更快）
        cursor.execute(f"TRUNCATE TABLE {table_name}")
        logger.info(f"✅ 表 {table_name} 数据已清空")
        return True
    except Exception as e:
        # 如果TRUNCATE失败，尝试使用DELETE
        try:
            cursor.execute(f"DELETE FROM {table_name}")
            logger.info(f"✅ 表 {table_name} 数据已清空（使用DELETE）")
            return True
        except Exception as e2:
            logger.error(f"❌ 清空表 {table_name} 失败: {e2}")
            return False

def main():
    """主函数"""
    print("🧹 APS 数据库表数据清空工具（扩展版）")
    print("=" * 80)
    
    # 需要清空的表分组
    table_groups = {
        '基础数据表 (原7个表)': [
            'et_wait_lot',      # 等待批次表
            'et_uph_eqp',       # UPH设备表
            'et_recipe_file',   # 配方文件表
            'et_ft_test_spec',  # 测试规范表
            'ct',               # CT数据表
            'wip_lot',          # WIP批次表
            'eqp_status'        # 设备状态表
        ],
        'Done Lots页面相关表': [
            'lotprioritydone',          # 已排产批次表（主要数据源）
            'final_scheduling_result',  # 最终排产调整结果表
            'scheduling_sessions',      # 排产调整会话管理表
            'adjustment_operations'     # 调整操作历史表
        ],
        'Failed Lots页面相关表': [
            'scheduling_failed_lots'    # 排产失败记录表
        ],
        '统一管理相关表': [
            'unified_lot_management'    # 统一批次管理表
        ]
    }
    
    # 展示所有表
    all_tables = []
    table_count = 0
    for group_name, tables in table_groups.items():
        print(f"\n📋 {group_name}:")
        for table in tables:
            table_count += 1
            print(f"   {table_count}. {table}")
            all_tables.append(table)
    
    print(f"\n📊 总计: {len(all_tables)} 个表")
    print("\n⚠️ 警告: 此操作将永久删除表中的所有数据！")
    print("💡 建议: 操作前请确保已有数据备份")
    
    # 用户确认
    print("\n选择操作模式:")
    print("1. 清空所有表数据（不备份）")
    print("2. 备份后清空所有表数据（推荐）")
    print("3. 仅查看表状态")
    print("4. 选择性清空表（交互模式）")
    print("5. 取消操作")
    
    choice = input("\n请选择 (1/2/3/4/5): ").strip()
    
    if choice == '5':
        print("❌ 操作已取消")
        return
    
    try:
        # 连接数据库
        connection = get_mysql_connection()
        cursor = connection.cursor()
        
        # 检查表状态
        print(f"\n📊 检查表状态:")
        table_status = {}
        total_records = 0
        
        for group_name, tables in table_groups.items():
            print(f"\n  {group_name}:")
            for table_name in tables:
                if check_table_exists(cursor, table_name):
                    row_count = get_table_row_count(cursor, table_name)
                    table_status[table_name] = row_count
                    total_records += row_count
                    print(f"     ✅ {table_name}: {row_count:,} 条记录")
                else:
                    table_status[table_name] = -1
                    print(f"     ❌ {table_name}: 表不存在")
        
        print(f"\n📊 总记录数: {total_records:,} 条")
        
        if choice == '3':
            print("\n📋 表状态检查完成")
            return
        
        # 选择性清空模式
        if choice == '4':
            tables_to_clear = []
            print(f"\n🎯 选择性清空模式:")
            print("请选择要清空的表组:")
            
            for i, (group_name, tables) in enumerate(table_groups.items(), 1):
                valid_tables = [t for t in tables if table_status.get(t, -1) >= 0]
                total_records_in_group = sum(table_status.get(t, 0) for t in valid_tables)
                print(f"  {i}. {group_name} ({len(valid_tables)}个表, {total_records_in_group:,}条记录)")
            
            print(f"  {len(table_groups)+1}. 全部表")
            print(f"  0. 返回主菜单")
            
            group_choice = input(f"\n请选择表组 (0-{len(table_groups)+1}): ").strip()
            
            if group_choice == '0':
                return main()
            elif group_choice == str(len(table_groups)+1):
                tables_to_clear = all_tables
            else:
                try:
                    group_index = int(group_choice) - 1
                    if 0 <= group_index < len(table_groups):
                        group_name = list(table_groups.keys())[group_index]
                        tables_to_clear = table_groups[group_name]
                        print(f"✅ 已选择: {group_name}")
                    else:
                        print("❌ 无效选择")
                        return
                except ValueError:
                    print("❌ 无效选择")
                    return
        else:
            tables_to_clear = all_tables
        
        # 执行清空操作
        if choice in ['1', '2', '4']:
            print(f"\n🧹 开始清空表数据...")
            
            cleared_tables = []
            failed_tables = []
            backup_info = []
            total_cleared_records = 0
            
            for table_name in tables_to_clear:
                if table_status.get(table_name, -1) == -1:
                    print(f"⏭️ 跳过不存在的表: {table_name}")
                    continue
                
                if table_status.get(table_name, 0) == 0:
                    print(f"⏭️ 跳过空表: {table_name}")
                    continue
                
                record_count = table_status[table_name]
                print(f"\n🔄 处理表: {table_name} ({record_count:,} 条记录)")
                
                # 备份数据（如果选择了备份模式）
                backup_table = None
                if choice == '2':
                    backup_table = backup_table_data(cursor, table_name)
                    if backup_table:
                        backup_info.append(f"{table_name} -> {backup_table}")
                
                # 清空数据
                if clear_table_data(cursor, table_name):
                    cleared_tables.append(table_name)
                    total_cleared_records += record_count
                else:
                    failed_tables.append(table_name)
            
            # 输出结果
            print(f"\n" + "="*80)
            print(f"🎉 数据清空操作完成！")
            print(f"="*80)
            print(f"📊 操作统计:")
            print(f"   ✅ 成功清空: {len(cleared_tables)} 个表")
            print(f"   ❌ 清空失败: {len(failed_tables)} 个表")
            print(f"   🗑️ 总清空记录数: {total_cleared_records:,} 条")
            
            if cleared_tables:
                print(f"\n✅ 成功清空的表:")
                for group_name, tables in table_groups.items():
                    group_cleared = [t for t in tables if t in cleared_tables]
                    if group_cleared:
                        print(f"   📋 {group_name}:")
                        for table in group_cleared:
                            print(f"      • {table}")
            
            if failed_tables:
                print(f"\n❌ 清空失败的表:")
                for table in failed_tables:
                    print(f"   • {table}")
            
            if backup_info:
                print(f"\n💾 备份信息:")
                for info in backup_info:
                    print(f"   • {info}")
                print(f"\n💡 如需恢复数据，可使用备份表")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        logger.error(f"❌ 操作失败: {e}")
        print(f"\n💥 操作失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

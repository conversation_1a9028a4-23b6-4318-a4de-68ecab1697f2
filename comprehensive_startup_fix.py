#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
APS平台 - 综合启动问题诊断和修复脚本
适用于开发环境和exe环境
"""

import os
import sys
import logging
import traceback
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(levelname)s: %(message)s'
)
logger = logging.getLogger(__name__)

def print_banner():
    """打印启动横幅"""
    print("=" * 80)
    print("🚀 APS车规芯片终测智能调度平台 - 综合启动诊断与修复工具")
    print("=" * 80)
    print(f"📅 Python版本: {sys.version}")
    print(f"📁 当前目录: {os.getcwd()}")
    
    if getattr(sys, 'frozen', False):
        print("🎯 运行环境: exe打包环境")
        print(f"📦 exe路径: {sys.executable}")
    else:
        print("🎯 运行环境: 开发环境")

def diagnose_config_file():
    """诊断配置文件问题"""
    print("\n" + "=" * 60)
    print("📋 配置文件诊断")
    print("=" * 60)
    
    config_found = False
    config_path = None
    
    # 可能的配置文件路径
    if getattr(sys, 'frozen', False):
        # exe环境
        exe_dir = os.path.dirname(sys.executable)
        possible_paths = [
            os.path.join(exe_dir, 'config.ini'),
            os.path.join(exe_dir, '..', 'config.ini'),
            'config.ini'
        ]
    else:
        # 开发环境
        possible_paths = [
            'config.ini',
            os.path.join(os.getcwd(), 'config.ini')
        ]
    
    print("🔍 查找配置文件:")
    for path in possible_paths:
        abs_path = os.path.abspath(path)
        if os.path.exists(abs_path):
            print(f"✅ 找到: {abs_path}")
            config_found = True
            config_path = abs_path
            break
        else:
            print(f"❌ 未找到: {abs_path}")
    
    if not config_found:
        print("\n❌ 严重错误：未找到config.ini配置文件！")
        return fix_missing_config()
    
    return validate_config_content(config_path)

def fix_missing_config():
    """修复缺失的配置文件"""
    print("\n🔧 配置文件修复")
    print("-" * 40)
    
    # 确定配置文件应该存放的位置
    if getattr(sys, 'frozen', False):
        # exe环境：放在exe同目录
        exe_dir = os.path.dirname(sys.executable)
        config_path = os.path.join(exe_dir, 'config.ini')
    else:
        # 开发环境：放在项目根目录
        config_path = os.path.join(os.getcwd(), 'config.ini')
    
    # 创建默认配置文件
    default_config = """# =================================================================
# APS 车规芯片终测智能调度平台配置文件
# 请根据您的实际环境修改以下配置
# =================================================================

[DATABASE]
# 数据库主机地址
host = localhost

# 数据库端口
port = 3306

# 数据库用户名
user = root

# 数据库密码（请修改为您的实际密码）
password = WWWwww123!

# 数据库名称
database = aps

# 字符集
charset = utf8mb4

[APPLICATION]
# 应用监听地址
host = 0.0.0.0

# 应用端口
port = 5000

# 调试模式（生产环境请设置为false）
debug = false

[SYSTEM]
# 时区设置
timezone = Asia/Shanghai

# 日志级别
log_level = INFO

# 最大工作线程数
max_workers = 10
"""
    
    try:
        with open(config_path, 'w', encoding='utf-8') as f:
            f.write(default_config)
        
        print(f"✅ 默认配置文件已创建: {config_path}")
        print("\n⚠️ 重要提醒：")
        print("1. 请打开配置文件修改数据库连接信息")
        print("2. 特别是数据库密码，请设置为您的实际MySQL密码")
        print("3. 如果MySQL不在本机，请修改host地址")
        
        return validate_config_content(config_path)
        
    except Exception as e:
        print(f"❌ 创建配置文件失败: {e}")
        return False

def validate_config_content(config_path):
    """验证配置文件内容"""
    print(f"\n📊 验证配置文件内容: {config_path}")
    print("-" * 60)
    
    try:
        import configparser
        config = configparser.ConfigParser()
        config.read(config_path, encoding='utf-8')
        
        # 检查必要的段
        if not config.has_section('DATABASE'):
            print("❌ 配置文件缺少[DATABASE]段")
            return False
        
        # 检查必要的字段
        required_fields = ['host', 'port', 'user', 'password', 'database']
        db_config = {}
        
        print("🔧 数据库配置:")
        for field in required_fields:
            if config.has_option('DATABASE', field):
                value = config.get('DATABASE', field)
                db_config[field] = value
                if field == 'password':
                    print(f"  {field}: {'*' * len(value) if value else '(空)'}")
                else:
                    print(f"  {field}: {value}")
            else:
                print(f"❌ 缺少必要字段: {field}")
                return False
        
        # 验证配置合理性
        issues = []
        if not db_config['password']:
            issues.append("数据库密码为空")
        
        if db_config['host'] in ['localhost', '127.0.0.1']:
            if not check_local_mysql():
                issues.append("配置为localhost但本地无MySQL服务")
        
        if issues:
            print("\n⚠️ 配置问题:")
            for issue in issues:
                print(f"  - {issue}")
            return False
        
        print("✅ 配置文件验证通过")
        return db_config
        
    except Exception as e:
        print(f"❌ 配置文件验证失败: {e}")
        return False

def check_local_mysql():
    """检查本地MySQL服务"""
    try:
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(3)
        result = sock.connect_ex(('localhost', 3306))
        sock.close()
        return result == 0
    except:
        return False

def test_database_connection(db_config):
    """测试数据库连接"""
    print("\n🔌 数据库连接测试")
    print("-" * 40)
    
    try:
        # 检查pymysql模块
        try:
            import pymysql
            print("✅ PyMySQL模块可用")
        except ImportError:
            print("❌ PyMySQL模块不可用，请安装: pip install pymysql")
            return False
        
        # 构建连接配置
        conn_config = {
            'host': db_config['host'],
            'port': int(db_config['port']),
            'user': db_config['user'],
            'password': db_config['password'],
            'charset': 'utf8mb4'
        }
        
        print(f"正在连接到: {conn_config['host']}:{conn_config['port']}")
        
        # 测试MySQL服务器连接
        connection = pymysql.connect(**conn_config)
        print("✅ MySQL服务器连接成功")
        
        # 检查数据库是否存在
        cursor = connection.cursor()
        cursor.execute("SHOW DATABASES")
        databases = [row[0] for row in cursor.fetchall()]
        
        if db_config['database'] in databases:
            print(f"✅ 数据库 '{db_config['database']}' 存在")
        else:
            print(f"❌ 数据库 '{db_config['database']}' 不存在")
            print(f"📋 可用数据库: {', '.join(databases)}")
            # 尝试创建数据库
            if create_database(cursor, db_config['database']):
                print(f"✅ 数据库 '{db_config['database']}' 创建成功")
            else:
                connection.close()
                return False
        
        # 测试数据库连接
        conn_config['database'] = db_config['database']
        db_connection = pymysql.connect(**conn_config)
        
        # 检查关键表
        cursor = db_connection.cursor()
        cursor.execute("SHOW TABLES")
        tables = [row[0] for row in cursor.fetchall()]
        
        if tables:
            print(f"✅ 数据库包含 {len(tables)} 个表")
            critical_tables = ['users', 'user_permissions']
            missing_critical = [table for table in critical_tables if table not in tables]
            
            if missing_critical:
                print(f"⚠️ 缺少关键表: {', '.join(missing_critical)}")
                print("💡 建议运行数据库初始化: python tools/database/init_db.py")
            else:
                print("✅ 关键业务表检查通过")
        else:
            print("⚠️ 数据库为空，需要初始化")
            print("💡 建议运行数据库初始化: python tools/database/init_db.py")
        
        connection.close()
        db_connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        print_connection_help()
        return False

def create_database(cursor, database_name):
    """创建数据库"""
    try:
        cursor.execute(f"CREATE DATABASE {database_name} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        return True
    except Exception as e:
        print(f"❌ 数据库创建失败: {e}")
        return False

def print_connection_help():
    """打印连接帮助信息"""
    print("\n🔧 数据库连接问题解决方案:")
    print("1. 检查MySQL服务是否启动")
    print("   - Windows: 服务管理器 -> MySQL")
    print("   - Linux: sudo systemctl status mysql")
    print("2. 验证数据库连接信息")
    print("   - 主机地址、端口、用户名、密码")
    print("3. 检查网络连接和防火墙")
    print("   - ping 数据库服务器")
    print("   - telnet 主机 3306")
    print("4. 确认MySQL用户权限")
    print("   - GRANT ALL PRIVILEGES ON aps.* TO 'root'@'%'")

def test_app_dependencies():
    """测试应用依赖"""
    print("\n📦 应用依赖检查")
    print("-" * 40)
    
    critical_modules = [
        'flask',
        'pymysql', 
        'sqlalchemy',
        'configparser'
    ]
    
    missing_modules = []
    
    for module in critical_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module}")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\n❌ 缺少依赖模块: {', '.join(missing_modules)}")
        print("🔧 解决方案: pip install -r requirements.txt")
        return False
    
    # 测试应用模块导入
    try:
        import sys
        sys.path.append(os.getcwd())
        from app.utils.unified_db_config import get_database_config
        print("✅ 应用模块导入成功")
        return True
    except Exception as e:
        print(f"❌ 应用模块导入失败: {e}")
        return False

def generate_startup_helper():
    """生成启动帮助脚本"""
    print("\n📝 生成启动帮助脚本")
    print("-" * 40)
    
    if getattr(sys, 'frozen', False):
        # exe环境
        exe_dir = os.path.dirname(sys.executable)
        exe_name = os.path.basename(sys.executable)
        
        startup_script = f"""@echo off
chcp 65001 >nul
title APS车规芯片终测智能调度平台

echo ===============================================
echo APS车规芯片终测智能调度平台
echo ===============================================
echo.

cd /d "{exe_dir}"

echo 检查配置文件...
if not exist "config.ini" (
    echo 错误：配置文件config.ini不存在
    echo 请运行comprehensive_startup_fix.exe进行配置
    pause
    exit /b 1
)

echo 启动应用...
"{exe_name}"

if %ERRORLEVEL% neq 0 (
    echo.
    echo 应用启动失败，错误代码: %ERRORLEVEL%
    echo 请运行comprehensive_startup_fix.exe进行诊断
    pause
)
"""
        
        script_path = os.path.join(exe_dir, 'start_aps.bat')
        
    else:
        # 开发环境
        startup_script = """#!/bin/bash
# APS车规芯片终测智能调度平台启动脚本

echo "==============================================="
echo "APS车规芯片终测智能调度平台"
echo "==============================================="
echo

# 检查配置文件
if [ ! -f "config.ini" ]; then
    echo "错误：配置文件config.ini不存在"
    echo "请运行: python comprehensive_startup_fix.py"
    exit 1
fi

# 检查虚拟环境
if [ -d "venv" ]; then
    echo "激活虚拟环境..."
    source venv/bin/activate
fi

# 启动应用
echo "启动应用..."
python run.py

if [ $? -ne 0 ]; then
    echo
    echo "应用启动失败"
    echo "请运行: python comprehensive_startup_fix.py"
    read -p "按Enter键退出..."
fi
"""
        
        script_path = os.path.join(os.getcwd(), 'start_aps.sh')
    
    try:
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(startup_script)
        
        # 为Linux脚本添加执行权限
        if script_path.endswith('.sh'):
            os.chmod(script_path, 0o755)
        
        print(f"✅ 启动脚本已生成: {script_path}")
        return True
        
    except Exception as e:
        print(f"❌ 启动脚本生成失败: {e}")
        return False

def print_final_summary(config_valid, db_connected, deps_ok):
    """打印最终总结"""
    print("\n" + "=" * 60)
    print("📋 诊断总结")
    print("=" * 60)
    
    print(f"📋 配置文件: {'✅ 正常' if config_valid else '❌ 异常'}")
    print(f"🔌 数据库连接: {'✅ 正常' if db_connected else '❌ 异常'}")
    print(f"📦 依赖模块: {'✅ 正常' if deps_ok else '❌ 异常'}")
    
    if config_valid and db_connected and deps_ok:
        print("\n🎉 所有检查通过！应用应该能够正常启动")
        print("\n🚀 启动方式:")
        if getattr(sys, 'frozen', False):
            print("1. 双击exe文件启动")
            print("2. 使用生成的start_aps.bat脚本")
        else:
            print("1. 命令行运行: python run.py")
            print("2. 使用生成的start_aps.sh脚本")
    else:
        print("\n❌ 存在问题需要解决")
        print("\n🔧 建议操作:")
        if not config_valid:
            print("- 修复配置文件问题")
        if not db_connected:
            print("- 解决数据库连接问题")
        if not deps_ok:
            print("- 安装缺失的依赖模块")

def main():
    """主函数"""
    try:
        print_banner()
        
        # 依次执行诊断
        config_valid = diagnose_config_file()
        deps_ok = test_app_dependencies()
        
        if config_valid and isinstance(config_valid, dict):
            db_connected = test_database_connection(config_valid)
        else:
            db_connected = False
        
        # 生成启动脚本
        if config_valid and db_connected and deps_ok:
            generate_startup_helper()
        
        # 打印总结
        print_final_summary(
            bool(config_valid), 
            db_connected, 
            deps_ok
        )
        
    except KeyboardInterrupt:
        print("\n\n用户中断操作")
    except Exception as e:
        print(f"\n❌ 诊断过程中发生意外错误: {e}")
        print("\n🔍 详细错误信息:")
        traceback.print_exc()
    finally:
        input("\n按Enter键退出...")

if __name__ == "__main__":
    main() 
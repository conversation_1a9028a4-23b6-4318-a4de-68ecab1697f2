#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
APS 统一配置管理器
解决配置分散和环境变量加载问题
"""

import os
import sys
from pathlib import Path
from typing import Optional, Dict, Any
from dotenv import load_dotenv

class UnifiedConfigManager:
    """统一配置管理器 - 确保.env文件正确加载"""
    
    def __init__(self, env_file: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            env_file: .env文件路径，默认为项目根目录下的.env
        """
        self.base_dir = Path(__file__).parent.parent
        self.env_file = env_file or self.base_dir / '.env'
        
        # 强制加载.env文件
        self._load_env_file()
        
        # 初始化所有配置
        self._init_all_configs()
    
    def _load_env_file(self):
        """强制加载.env文件"""
        if os.path.exists(self.env_file):
            print(f"[OK] Environment file loaded: {self.env_file}")
            # 显示关键环境变量状态
            key_vars = ['DB_HOST', 'DB_USER', 'DB_PASSWORD', 'DB_NAME']
            for var in key_vars:
                value = os.getenv(var, '')
                if value:
                    print(f"   {var}=***" if 'PASSWORD' in var else f"   {var}={value}")
                else:
                    print(f"   [WARN] {var}=not set")
        else:
            print(f"[WARN] Environment file not found: {self.env_file}")
    
    def _init_all_configs(self):
        """初始化所有配置项"""
        # ==============================================
        # 数据库配置 (MySQL)
        # ==============================================
        self.MYSQL_HOST = os.environ.get('MYSQL_HOST', 'localhost')
        self.MYSQL_PORT = int(os.environ.get('MYSQL_PORT', 3306))
        self.MYSQL_USER = os.environ.get('MYSQL_USER', 'root')
        self.MYSQL_PASSWORD = os.environ.get('MYSQL_PASSWORD', 'WWWwww123!')
        self.MYSQL_CHARSET = os.environ.get('MYSQL_CHARSET', 'utf8mb4')
        
        # 通用数据库配置
        self.DB_HOST = os.environ.get('DB_HOST', self.MYSQL_HOST)
        self.DB_PORT = int(os.environ.get('DB_PORT', self.MYSQL_PORT))
        self.DB_USER = os.environ.get('DB_USER', self.MYSQL_USER)
        self.DB_PASSWORD = os.environ.get('DB_PASSWORD', self.MYSQL_PASSWORD)
        self.DB_NAME = os.environ.get('DB_NAME', 'aps')
        self.DB_CHARSET = os.environ.get('DB_CHARSET', self.MYSQL_CHARSET)
        
        # ==============================================
        # Flask应用配置
        # ==============================================
        self.FLASK_HOST = os.environ.get('FLASK_HOST', '0.0.0.0')  # 允许外部访问
        self.FLASK_PORT = int(os.environ.get('FLASK_PORT', 5000))
        self.FLASK_ENV = os.environ.get('FLASK_ENV', 'development')
        self.SECRET_KEY = os.environ.get('SECRET_KEY', 'dev-secret-key-change-in-production')
        
        # ==============================================
        # 系统配置
        # ==============================================
        self.TIMEZONE = os.environ.get('TIMEZONE', 'Asia/Shanghai')
        self.DEFAULT_PAGE_SIZE = int(os.environ.get('DEFAULT_PAGE_SIZE', 1000))
        self.DEFAULT_UPH = int(os.environ.get('DEFAULT_UPH', 1000))
        self.MAX_WORKERS = int(os.environ.get('MAX_WORKERS', 10))
        
        # ==============================================
        # 文件路径配置
        # ==============================================
        self.LOG_DIR = os.environ.get('LOG_DIR', 'logs')
        self.UPLOAD_DIR = os.environ.get('UPLOAD_DIR', 'uploads')
        self.DOWNLOAD_DIR = os.environ.get('DOWNLOAD_DIR', 'downloads')
        self.INSTANCE_DIR = os.environ.get('INSTANCE_DIR', 'instance')
        self.STATIC_EXPORTS_DIR = os.environ.get('STATIC_EXPORTS_DIR', 'static/exports')
        self.SQLITE_DB_PATH = os.environ.get('SQLITE_DB_PATH', 'instance/aps.db')
        self.EXCEL_BASE_PATH = os.environ.get('EXCEL_BASE_PATH', 'Excellist2025.06.05')
        
        # ==============================================
        # 管理员配置
        # ==============================================
        self.ADMIN_USERNAME = os.environ.get('ADMIN_USERNAME', 'admin')
        self.ADMIN_PASSWORD = os.environ.get('ADMIN_PASSWORD', 'admin')
        
        # ==============================================
        # 业务常量配置
        # ==============================================
        self.LARGE_BATCH_THRESHOLD = int(os.environ.get('LARGE_BATCH_THRESHOLD', 10000))
        self.MEDIUM_BATCH_THRESHOLD = int(os.environ.get('MEDIUM_BATCH_THRESHOLD', 1000))
        self.BASE_VALUE_RATE = float(os.environ.get('BASE_VALUE_RATE', 10000.0))
        self.MAX_PRIORITY_SCORE = float(os.environ.get('MAX_PRIORITY_SCORE', 100.0))
        self.MAX_PROCESSING_TIME_HOURS = int(os.environ.get('MAX_PROCESSING_TIME_HOURS', 24))
        
        # ==============================================
        # 调试和性能配置
        # ==============================================
        self.DEBUG = os.environ.get('DEBUG', 'True').lower() == 'true'
        self.TESTING = os.environ.get('TESTING', 'False').lower() == 'true'
        self.LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')
        self.QUIET_STARTUP = os.environ.get('QUIET_STARTUP', '0') == '1'
        
        # ==============================================
        # 安全配置
        # ==============================================
        self.CORS_ENABLED = os.environ.get('CORS_ENABLED', 'True').lower() == 'true'
        self.CSRF_ENABLED = os.environ.get('CSRF_ENABLED', 'True').lower() == 'true'
        self.SESSION_TIMEOUT = int(os.environ.get('SESSION_TIMEOUT', 3600))
        
        # ==============================================
        # 性能配置
        # ==============================================
        self.CACHE_ENABLED = os.environ.get('CACHE_ENABLED', 'True').lower() == 'true'
        self.CACHE_TIMEOUT = int(os.environ.get('CACHE_TIMEOUT', 300))
        self.DB_POOL_SIZE = int(os.environ.get('DB_POOL_SIZE', 10))
        self.DB_POOL_TIMEOUT = int(os.environ.get('DB_POOL_TIMEOUT', 30))
        
        # ==============================================
        # API配置
        # ==============================================
        self.API_PREFIX = os.environ.get('API_PREFIX', '/api')
        self.API_VERSION = os.environ.get('API_VERSION', 'v2')
        self.API_HOST = os.environ.get('API_HOST', '0.0.0.0')
        self.API_PORT = int(os.environ.get('API_PORT', 5000))
        self.API_DOCS_ENABLED = os.environ.get('API_DOCS_ENABLED', 'True').lower() == 'true'
        self.API_TIMEOUT = int(os.environ.get('API_TIMEOUT', 30))
        self.API_MAX_CONTENT_LENGTH = int(os.environ.get('API_MAX_CONTENT_LENGTH', 16777216))
        
        # API CORS配置
        self.API_CORS_ENABLED = os.environ.get('API_CORS_ENABLED', 'True').lower() == 'true'
        self.API_CORS_ORIGINS = os.environ.get('API_CORS_ORIGINS', '*').split(',')
        self.API_CORS_METHODS = os.environ.get('API_CORS_METHODS', 'GET,POST,PUT,DELETE,OPTIONS').split(',')
        
        # API认证配置
        self.API_AUTH_ENABLED = os.environ.get('API_AUTH_ENABLED', 'True').lower() == 'true'
        self.API_TOKEN_EXPIRE_HOURS = int(os.environ.get('API_TOKEN_EXPIRE_HOURS', 24))
        self.API_REFRESH_TOKEN_EXPIRE_DAYS = int(os.environ.get('API_REFRESH_TOKEN_EXPIRE_DAYS', 30))
        
        # ==============================================
        # Redis配置
        # ==============================================
        self.REDIS_HOST = os.environ.get('REDIS_HOST', 'localhost')
        self.REDIS_PORT = int(os.environ.get('REDIS_PORT', 6379))
        self.REDIS_DB = int(os.environ.get('REDIS_DB', 0))
        self.REDIS_PASSWORD = os.environ.get('REDIS_PASSWORD', '')
    
    @property
    def DATABASE_URI(self) -> str:
        """构建数据库连接URI"""
        return f"mysql+pymysql://{self.DB_USER}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}?charset={self.DB_CHARSET}"
    
    @property
    def REDIS_URL(self) -> str:
        """构建Redis连接URL"""
        if self.REDIS_PASSWORD:
            return f"redis://:{self.REDIS_PASSWORD}@{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"
        return f"redis://{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"
    
    def get_log_file_path(self, filename: str = 'app.log') -> Path:
        """获取日志文件路径"""
        log_dir = self.base_dir / self.LOG_DIR
        log_dir.mkdir(parents=True, exist_ok=True)
        return log_dir / filename
    
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        return {
            'flask_host': self.FLASK_HOST,
            'flask_port': self.FLASK_PORT,
            'mysql_host': self.MYSQL_HOST,
            'mysql_port': self.MYSQL_PORT,
            'debug': self.DEBUG,
            'env_file': str(self.env_file),
            'env_file_exists': self.env_file.exists()
        }
    
    def validate_config(self) -> bool:
        """验证配置有效性"""
        try:
            # 尝试加载.env文件
            from dotenv import load_dotenv
            if load_dotenv():
                print("[OK] .env file loaded successfully")
            else:
                print("[WARN] .env file not found, using environment variables")
                
        except ImportError:
            print("[WARN] python-dotenv not installed, using environment variables only")
            
        try:
            # 验证关键配置
            if not self.DB_PASSWORD:
                print("[ERROR] Database password not set")
                return False
                
            if not (1 <= self.DB_PORT <= 65535):
                print(f"[ERROR] Invalid database port: {self.DB_PORT}")
                return False
                
            if not (1 <= self.FLASK_PORT <= 65535):
                print(f"[ERROR] Invalid Flask port: {self.FLASK_PORT}")
                return False
                
            print("[OK] Configuration validation passed")
            return True
        except Exception as e:
            print(f"[ERROR] Configuration validation failed: {e}")
            return False

# 全局配置实例
_config_instance = None

def get_unified_config(env_file: Optional[str] = None) -> UnifiedConfigManager:
    """获取统一配置实例（单例模式）"""
    global _config_instance
    if _config_instance is None:
        _config_instance = UnifiedConfigManager(env_file)
    return _config_instance

# 向后兼容接口
def get_config() -> UnifiedConfigManager:
    """获取配置实例（兼容旧接口）"""
    return get_unified_config()

if __name__ == '__main__':
    # 测试配置加载
    print("🔧 测试统一配置管理器")
    print("=" * 50)
    
    config = get_unified_config()
    
    print("\n📋 配置摘要:")
    summary = config.get_config_summary()
    for key, value in summary.items():
        print(f"  {key}: {value}")
    
    print(f"\n🌐 Flask服务器配置:")
    print(f"  地址: {config.FLASK_HOST}:{config.FLASK_PORT}")
    print(f"  数据库: {config.MYSQL_HOST}:{config.MYSQL_PORT}")
    
    # 验证配置
    print(f"\n🔍 配置验证:")
    config.validate_config() 
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
高性能邮件处理器 - 大幅提升邮件处理效率

优化策略：
1. 批量获取邮件头部，避免逐一下载完整邮件
2. 预筛选减少无效处理
3. 多线程并发处理
4. 智能缓存和去重
5. 实时进度反馈
6. MD5文件检查，避免重复处理相同文件 (新增)
7. 处理时间预测功能 (新增)
8. 优化批量处理策略 (新增)
"""

import imaplib
import email
import os
import logging
import hashlib
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
import time
from typing import List, Dict, Any, Callable, Optional
import threading
from email.header import decode_header
import re
import uuid
import pymysql
from app.utils.db_connection_pool import get_db_connection_context, get_db_connection
import json

from app.models import EmailConfig, EmailAttachment
from app import db
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

logger = logging.getLogger(__name__)

class HighPerformanceEmailProcessor:
    def __init__(self, config_or_id):
        # 支持传入config对象或config_id
        if isinstance(config_or_id, int):
            # 传入的是config_id，需要从数据库获取config
            from app.models import EmailConfig
            self.config = EmailConfig.query.get(config_or_id)
            if not self.config:
                raise ValueError(f"EmailConfig with id {config_or_id} not found")
        else:
            # 传入的是config对象
            self.config = config_or_id
        self.imap = None
        self.progress_callback = None
        self.stop_event = threading.Event()
        
        # 性能统计 (新增)
        self.performance_stats = {
            'start_time': None,
            'processed_files': 0,
            'total_files': 0,
            'avg_file_time': 0,
            'md5_cache_hits': 0,
            'duplicate_files': 0
        }
        
        # MD5缓存 (新增)
        self.md5_cache = {}
        self.duplicate_files = set()
        
        # 为多线程环境创建独立的数据库会话
        self._setup_database_session()
        
        # 初始化性能数据 (新增)
        self._load_performance_history()
        
    def _setup_database_session(self):
        """为多线程环境设置独立的数据库会话 - 修正：使用MySQL aps数据库"""
        try:
            # 使用统一的配置管理器获取数据库连接
            from config.enhanced_config import config
            mysql_uri = f"mysql+pymysql://{config.DB_USER}:{config.DB_PASSWORD}@{config.DB_HOST}:{config.DB_PORT}/aps?charset={config.DB_CHARSET}"
            
            self.engine = create_engine(mysql_uri, pool_recycle=3600, echo=False)
            self.SessionLocal = sessionmaker(bind=self.engine)
            logger.info("✅ 成功设置MySQL aps数据库会话")
                
        except Exception as e:
            logger.error(f"❌ 设置MySQL数据库会话失败: {e}")
            self.engine = None
            self.SessionLocal = None
    
    def _load_performance_history(self):
        """加载历史性能数据用于预测 (新增)"""
        try:
            if self.SessionLocal:
                session = self.SessionLocal()
                try:
                    # 查询最近7天的处理统计
                    sql = text("""
                        SELECT 
                            DATE(created_at) as process_date,
                            COUNT(*) as file_count,
                            AVG(file_size) as avg_file_size,
                            SUM(file_size) as total_size
                        FROM email_attachments 
                        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                        GROUP BY DATE(created_at)
                        ORDER BY process_date DESC
                    """)
                    
                    result = session.execute(sql)
                    rows = result.fetchall()
                    
                    if rows:
                        # 计算平均处理速度
                        total_files = sum(row[1] for row in rows)
                        avg_file_size = sum(row[2] for row in rows if row[2]) / len(rows)
                        
                        # 预估每个文件处理时间（基于历史数据）
                        self.performance_stats['avg_file_time'] = max(0.5, min(3.0, avg_file_size / 1024 / 1024))
                        logger.info(f"📊 加载历史性能数据: 平均文件大小 {avg_file_size/1024/1024:.2f}MB, 预估处理时间 {self.performance_stats['avg_file_time']:.1f}秒/文件")
                    
                finally:
                    session.close()
        except Exception as e:
            logger.warning(f"⚠️ 无法加载历史性能数据: {e}")
            self.performance_stats['avg_file_time'] = 1.0  # 默认值
    
    def _calculate_file_md5(self, file_content: bytes) -> str:
        """计算文件MD5值 (新增)"""
        return hashlib.md5(file_content).hexdigest()
    
    def _is_duplicate_file(self, file_content: bytes, filename: str) -> bool:
        """检查文件是否重复 (新增)"""
        try:
            file_md5 = self._calculate_file_md5(file_content)
            
            # 检查内存缓存
            if file_md5 in self.md5_cache:
                self.performance_stats['md5_cache_hits'] += 1
                logger.info(f"🔄 MD5缓存命中，跳过重复文件: {filename}")
                return True
            
            # 检查数据库
            if self.SessionLocal:
                session = self.SessionLocal()
                try:
                    sql = text("SELECT id FROM email_attachments WHERE file_md5 = :md5 LIMIT 1")
                    result = session.execute(sql, {'md5': file_md5})
                    
                    if result.fetchone():
                        self.md5_cache[file_md5] = True
                        self.performance_stats['duplicate_files'] += 1
                        logger.info(f"🔄 数据库检测到重复文件: {filename} (MD5: {file_md5[:8]}...)")
                        return True
                    
                    # 添加到缓存
                    self.md5_cache[file_md5] = False
                    return False
                    
                finally:
                    session.close()
        except Exception as e:
            logger.warning(f"⚠️ MD5检查失败: {e}")
            return False
        
        return False
    
    def _predict_processing_time(self, file_count: int) -> Dict[str, Any]:
        """预测处理时间 (新增)"""
        try:
            base_time = self.performance_stats['avg_file_time']
            
            # 考虑并发因子
            max_workers = min(3, max(1, file_count // 10))
            concurrent_factor = 1 / max_workers if max_workers > 1 else 1
            
            # 预测时间
            estimated_seconds = file_count * base_time * concurrent_factor
            
            # 添加网络延迟和开销
            network_overhead = file_count * 0.2  # 每文件200ms网络开销
            total_estimated = estimated_seconds + network_overhead
            
            return {
                'total_files': file_count,
                'estimated_seconds': int(total_estimated),
                'estimated_minutes': total_estimated / 60,
                'max_workers': max_workers,
                'avg_file_time': base_time
            }
        except Exception as e:
            logger.warning(f"⚠️ 时间预测失败: {e}")
            return {
                'total_files': file_count,
                'estimated_seconds': file_count * 2,
                'estimated_minutes': file_count * 2 / 60,
                'max_workers': 2,
                'avg_file_time': 2.0
            }
    
    def _optimize_batch_size(self, total_files: int) -> Dict[str, int]:
        """优化批量处理策略 (新增)"""
        try:
            # 根据文件数量动态调整批次大小和工作线程
            if total_files <= 5:
                return {'batch_size': total_files, 'max_workers': 1}
            elif total_files <= 20:
                return {'batch_size': 5, 'max_workers': 2}
            elif total_files <= 50:
                return {'batch_size': 10, 'max_workers': 3}
            else:
                return {'batch_size': 15, 'max_workers': 4}
        except Exception:
            return {'batch_size': 10, 'max_workers': 2}
        
    def set_progress_callback(self, callback: Callable):
        """设置进度回调函数"""
        self.progress_callback = callback
        
    def connect(self) -> bool:
        """连接到邮箱服务器"""
        try:
            logger.info(f"🚀 高性能处理器连接: {self.config.server}:{self.config.port}")
            
            self.imap = imaplib.IMAP4_SSL(self.config.server, self.config.port)
            self.imap.login(self.config.email, self.config.password)
            
            logger.info(f"✅ 高性能连接成功: {self.config.email}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 连接失败: {str(e)}")
            return False
    
    def disconnect(self):
        """断开连接"""
        if self.imap:
            try:
                self.imap.close()
                self.imap.logout()
            except:
                pass
                
    def _update_progress(self, current: int, total: int, message: str = "", extra_info: Dict = None):
        """更新进度 - 增强版，包含预测信息"""
        if self.progress_callback:
            progress = int((current / total) * 100) if total > 0 else 0
            
            progress_data = {
                'current': current,
                'total': total, 
                'progress': progress,
                'message': message
            }
            
            # 添加额外信息
            if extra_info:
                progress_data.update(extra_info)
            
            # 添加性能统计
            if self.performance_stats['start_time']:
                elapsed = time.time() - self.performance_stats['start_time']
                progress_data.update({
                    'elapsed_seconds': int(elapsed),
                    'md5_cache_hits': self.performance_stats['md5_cache_hits'],
                    'duplicate_files': self.performance_stats['duplicate_files']
                })
            
            self.progress_callback(progress_data)
    
    def process_fast(self, days: int = 3) -> Dict[str, Any]:
        """增强版快速处理邮件"""
        self.performance_stats['start_time'] = time.time()
        
        try:
            # 智能连接管理 - 避免重复连接
            connection_established = False
            if not self.imap:
                logger.info("📶 开始连接邮箱服务器...")
                if not self.connect():
                    return {'success': False, 'error': '连接失败'}
                connection_established = True
            else:
                # 检查现有连接是否有效
                try:
                    # 尝试发送一个简单的NOOP命令来测试连接
                    self.imap.noop()
                    logger.info("📶 使用现有有效连接")
                    connection_established = True
                except Exception as e:
                    logger.warning(f"📶 现有连接无效，重新连接: {e}")
                    self.imap = None  # 清除无效连接
                    if not self.connect():
                        return {'success': False, 'error': '重新连接失败'}
                    connection_established = True
            
            if not connection_established:
                return {'success': False, 'error': '无法建立邮箱连接'}
            
            # 1. 快速获取文件夹列表
            folders = self._get_folders()
            logger.info(f"📁 扫描文件夹: {len(folders)} 个")
            
            # 2. 批量获取邮件头部
            all_headers = []
            for i, folder in enumerate(folders):
                logger.info(f"📂 正在扫描文件夹 {i+1}/{len(folders)}: {folder}")
                self._update_progress(i, len(folders), f"扫描: {folder}")
                try:
                    headers = self._batch_get_headers(folder, days)
                    logger.info(f"📧 文件夹 {folder} 获取到 {len(headers)} 封邮件")
                    all_headers.extend(headers)
                except Exception as e:
                    logger.error(f"❌ 扫描文件夹 {folder} 失败: {e}")
                    continue
            
            logger.info(f"📊 总邮件数: {len(all_headers)}")
            
            # 3. 预筛选
            filtered = self._pre_filter(all_headers)
            logger.info(f"🎯 筛选后: {len(filtered)} 封")
            
            if not filtered:
                return {
                    'success': True,
                    'processed': 0,
                    'total': len(all_headers),
                    'downloaded': 0,
                    'message': '没有符合条件的邮件',
                    'time': time.time() - self.performance_stats['start_time']
                }
            
            # 4. 处理时间预测 (新增)
            prediction = self._predict_processing_time(len(filtered))
            logger.info(f"⏱️ 处理时间预测: {prediction['estimated_minutes']:.1f}分钟 ({prediction['total_files']}文件)")
            
            # 5. 优化批量处理策略 (新增)
            batch_config = self._optimize_batch_size(len(filtered))
            logger.info(f"🔧 批量处理配置: 批次大小={batch_config['batch_size']}, 工作线程={batch_config['max_workers']}")
            
            # 6. 并发处理附件（使用优化策略）
            result = self._process_attachments_concurrent_enhanced(filtered, batch_config, prediction)
            
            # 7. 统计结果
            result['time'] = time.time() - self.performance_stats['start_time']
            result['speed'] = len(filtered) / result['time'] if result['time'] > 0 else 0
            result['prediction'] = prediction
            result['performance_stats'] = self.performance_stats
            result['total'] = len(all_headers)  # 确保包含总数
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 处理失败: {e}")
            return {'success': False, 'error': str(e)}
        finally:
            # 确保连接正确关闭
            self.disconnect()
    
    def _get_folders(self) -> List[str]:
        """获取文件夹列表 - 修复重复文件夹问题"""
        try:
            status, folders = self.imap.list()
            if status != 'OK':
                return ['INBOX']
            
            folder_names = set()  # 使用set避免重复
            for folder in folders:
                try:
                    folder_str = folder.decode('utf-8', errors='ignore')
                    # 简单解析文件夹名
                    if '"' in folder_str:
                        parts = folder_str.split('"')
                        if len(parts) >= 3:
                            folder_name = parts[-2].strip()
                            if folder_name:  # 避免空文件夹名
                                folder_names.add(folder_name)
                except Exception:
                    continue
                        
            # 转换为列表并排序，确保INBOX在首位
            folder_list = list(folder_names)
            if 'INBOX' in folder_list:
                folder_list.remove('INBOX')
                folder_list = ['INBOX'] + sorted(folder_list)
            else:
                folder_list = ['INBOX'] + sorted(folder_list)
                
            logger.debug(f"📁 发现 {len(folder_list)} 个文件夹: {folder_list[:5]}...")  # 只显示前5个
            return folder_list
            
        except Exception as e:
            logger.warning(f"获取文件夹列表失败: {e}")
            return ['INBOX']
    
    def _batch_get_headers(self, folder: str, days: int) -> List[Dict]:
        """批量获取邮件头部 - 修复decode错误和重复扫描"""
        try:
            logger.info(f"🔍 开始处理文件夹: {folder}")
            
            # 选择文件夹
            status, _ = self.imap.select(folder)
            if status != 'OK':
                logger.warning(f"⚠️ 无法选择文件夹: {folder}")
                return []
            
            # 计算日期范围
            since_date = (datetime.now() - timedelta(days=days)).strftime("%d-%b-%Y")
            logger.info(f"🗓️ 搜索日期范围: {since_date} 之后")
            
            # 搜索邮件
            status, data = self.imap.search(None, f'(SINCE {since_date})')
            if status != 'OK':
                logger.warning(f"⚠️ 邮件搜索失败: {folder}")
                return []
            
            # 检查搜索结果
            if not data or not data[0]:
                logger.info(f"📭 文件夹 {folder} 没有符合条件的邮件")
                return []
                
            message_ids = data[0].split()
            if not message_ids:
                logger.info(f"📭 文件夹 {folder} 没有邮件ID")
                return []
            
            logger.info(f"📬 文件夹 {folder} 找到 {len(message_ids)} 封邮件")
            
            headers = []
            processed_count = 0
            
            # 批量获取头部信息，增加错误处理
            logger.info(f"📋 开始处理 {len(message_ids)} 封邮件的头部信息...")
            for i, msg_id in enumerate(message_ids):
                try:
                    # 安全处理message_id
                    if isinstance(msg_id, bytes):
                        msg_id_str = msg_id.decode('utf-8', errors='ignore')
                    else:
                        msg_id_str = str(msg_id)
                    
                    # 增加超时保护
                    status, data = self.imap.fetch(msg_id, '(RFC822.HEADER)')
                    if status != 'OK':
                        continue
                        
                    # 安全检查返回数据
                    if not data or len(data) == 0:
                        continue
                        
                    # 检查数据格式
                    mail_data = data[0]
                    if not isinstance(mail_data, tuple) or len(mail_data) < 2:
                        continue
                        
                    header_data = mail_data[1]
                    if not header_data or not isinstance(header_data, bytes):
                        continue
                        
                    # 解析邮件头
                    msg = email.message_from_bytes(header_data)
                    
                    # 安全提取邮件信息
                    sender = self._extract_email(msg.get('From', ''))
                    subject = self._decode_header(msg.get('Subject', ''))
                    date_str = msg.get('Date', '')
                    
                    headers.append({
                        'id': msg_id_str,
                        'folder': folder,
                        'sender': sender,
                        'subject': subject,
                        'date': date_str
                    })
                    
                    processed_count += 1
                    
                    # 每处理50封邮件显示一次进度
                    if processed_count % 50 == 0:
                        logger.info(f"📈 已处理 {processed_count}/{len(message_ids)} 封邮件...")
                    
                    # 避免处理过多邮件导致超时
                    if processed_count >= 1000:
                        logger.info(f"📧 文件夹 {folder} 邮件过多，限制处理1000封")
                        break
                    
                except Exception as e:
                    # 更详细的错误信息，但不输出完整堆栈
                    error_msg = str(e)
                    if 'decode' in error_msg.lower():
                        logger.warning(f"⚠️ 邮件ID解码问题 (ID: {msg_id}): 跳过")
                    else:
                        logger.warning(f"⚠️ 获取邮件头失败 (ID: {msg_id}): {error_msg}")
                    continue
            
            logger.info(f"📧 文件夹 {folder}: 成功获取 {len(headers)} 个邮件头")
            return headers
            
        except Exception as e:
            logger.error(f"批量获取头部失败 ({folder}): {e}")
            return []
    
    def _extract_email(self, sender_str: str) -> str:
        """提取邮箱地址"""
        if not sender_str:
            return ""
        
        # 先解码sender_str
        sender_str = self._decode_header(sender_str)
        
        # <AUTHOR> <EMAIL> 格式
        match = re.search(r'<([^>]+)>', sender_str)
        if match:
            return match.group(1)
        
        # 如果没有尖括号，检查是否直接是邮箱
        if '@' in sender_str:
            return sender_str.strip()
        
        return sender_str
    
    def _decode_header(self, header_value: str) -> str:
        """解码邮件头部 - 修复类型错误"""
        if not header_value:
            return ""
        
        try:
            # 如果已经是字符串，直接处理
            if isinstance(header_value, str):
                decoded_parts = decode_header(header_value)
            else:
                # 如果是其他类型，先转换为字符串
                decoded_parts = decode_header(str(header_value))
            
            result = ""
            
            for part, encoding in decoded_parts:
                if isinstance(part, bytes):
                    if encoding:
                        try:
                            result += part.decode(encoding, errors='ignore')
                        except (UnicodeDecodeError, LookupError):
                            result += part.decode('utf-8', errors='ignore')
                    else:
                        result += part.decode('utf-8', errors='ignore')
                elif isinstance(part, str):
                    result += part
                else:
                    # 处理其他类型（如int）
                    result += str(part)
            
            return result.strip()
        except Exception as e:
            logger.warning(f"解码邮件头部失败: {e}, 原始值: {header_value}")
            return str(header_value) if header_value else ""
    
    def _pre_filter(self, headers: List[Dict]) -> List[Dict]:
        """预筛选邮件"""
        # 发件人筛选
        target_senders = []
        if self.config.senders and self.config.senders.strip():
            target_senders = [s.strip().lower() for s in self.config.senders.split(';') if s.strip()]
        
        # 主题筛选
        target_subjects = []
        if self.config.subjects and self.config.subjects.strip():
            target_subjects = [s.strip() for s in self.config.subjects.split(';') if s.strip()]
        
        filtered = []
        for header in headers:
            # 发件人筛选
            sender_ok = not target_senders
            if target_senders:
                for target in target_senders:
                    if target in header['sender'].lower():
                        sender_ok = True
                        break
            
            if not sender_ok:
                continue
            
            # 主题筛选
            subject_ok = not target_subjects
            if target_subjects:
                for target in target_subjects:
                    if target in header['subject']:
                        subject_ok = True
                        break
            
            if subject_ok:
                filtered.append(header)
        
        return filtered
    
    def _process_attachments_concurrent(self, headers: List[Dict]) -> Dict[str, Any]:
        """并发处理附件（保留原方法作为回退）"""
        processed = 0
        skipped = 0
        
        # 使用较少的线程避免IMAP连接问题
        max_workers = 2
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交任务
            future_to_header = {
                executor.submit(self._process_single_email, header): header
                for header in headers
            }
            
            # 收集结果
            for i, future in enumerate(as_completed(future_to_header)):
                header = future_to_header[future]
                
                self._update_progress(
                    i + 1, len(headers),
                    f"处理: {header['subject'][:30]}..."
                )
                
                try:
                    result = future.result()
                    processed += result.get('processed', 0)
                    skipped += result.get('skipped', 0)
                except Exception as e:
                    logger.error(f"处理邮件失败: {e}")
                    skipped += 1
        
        return {
            'success': True,
            'processed': processed,
            'skipped': skipped,
            'total': len(headers)
        }
    
    def _process_attachments_concurrent_enhanced(self, headers: List[Dict], batch_config: Dict, prediction: Dict) -> Dict[str, Any]:
        """增强版并发处理附件 (新增)"""
        processed = 0
        skipped = 0
        
        max_workers = batch_config['max_workers']
        batch_size = batch_config['batch_size']
        
        logger.info(f"🚀 开始增强版并发处理: {len(headers)}个邮件, {max_workers}线程, 批次大小{batch_size}")
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 分批处理
            for batch_start in range(0, len(headers), batch_size):
                batch_end = min(batch_start + batch_size, len(headers))
                batch_headers = headers[batch_start:batch_end]
                
                # 提交批次任务
                future_to_header = {
                    executor.submit(self._process_single_email_enhanced, header): header
                    for header in batch_headers
                }
                
                # 收集批次结果
                for i, future in enumerate(as_completed(future_to_header)):
                    header = future_to_header[future]
                    
                    current_index = batch_start + i + 1
                    self._update_progress(
                        current_index, len(headers),
                        f"处理: {header['subject'][:30]}...",
                        {
                            'batch_info': f"批次 {batch_start//batch_size + 1}, 文件 {i+1}/{len(batch_headers)}",
                            'predicted_remaining': max(0, prediction['estimated_seconds'] - (time.time() - self.performance_stats['start_time']))
                        }
                    )
                    
                    try:
                        result = future.result()
                        processed += result.get('processed', 0)
                        skipped += result.get('skipped', 0)
                        self.performance_stats['processed_files'] += result.get('processed', 0)
                    except Exception as e:
                        logger.error(f"处理邮件失败: {e}")
                        skipped += 1
        
        return {
            'success': True,
            'processed': processed,
            'skipped': skipped,
            'total': len(headers),
            'batch_config': batch_config
        }
    
    def _process_single_email(self, header: Dict) -> Dict[str, int]:
        """处理单封邮件的附件"""
        try:
            # 创建独立的IMAP连接
            imap = imaplib.IMAP4_SSL(self.config.server, self.config.port)
            imap.login(self.config.email, self.config.password)
            
            try:
                imap.select(header['folder'])
                status, data = imap.fetch(header['id'], '(RFC822)')
                
                if status != 'OK':
                    return {'processed': 0, 'skipped': 1}
                
                msg = email.message_from_bytes(data[0][1])
                
                processed = 0
                skipped = 0
                
                for part in msg.walk():
                    if part.get_content_disposition() == 'attachment':
                        filename = part.get_filename()
                        if not filename:
                            continue
                        
                        filename = self._decode_header(filename)
                        
                        # Excel文件检查
                        if not filename.lower().endswith(('.xlsx', '.xls')):
                            skipped += 1
                            continue
                        
                        # 附件名称筛选 - 使用"生产订单"作为默认关键词
                        attachment_keywords = getattr(self.config, 'attachment_keywords', None) or "生产订单"
                        if attachment_keywords:
                            keywords = [k.strip() for k in attachment_keywords.split(';') if k.strip()]
                            if keywords and not any(k in filename for k in keywords):
                                skipped += 1
                                logger.info(f"❌ 附件名不符合: {filename}")
                                continue
                        
                        # 下载附件
                        if self._download_attachment(part, filename, header):
                            processed += 1
                        else:
                            skipped += 1
                
                return {'processed': processed, 'skipped': skipped}
                
            finally:
                imap.close()
                imap.logout()
                
        except Exception as e:
            logger.error(f"处理单邮件失败: {e}")
            return {'processed': 0, 'skipped': 1}
    
    def _process_single_email_enhanced(self, header: Dict) -> Dict[str, int]:
        """增强版处理单封邮件的附件 (新增)"""
        try:
            imap = imaplib.IMAP4_SSL(self.config.server, self.config.port)
            imap.login(self.config.email, self.config.password)
            
            try:
                imap.select(header['folder'])
                status, data = imap.fetch(header['id'], '(RFC822)')
                
                if status != 'OK':
                    return {'processed': 0, 'skipped': 1}
                
                msg = email.message_from_bytes(data[0][1])
                
                processed = 0
                skipped = 0
                
                for part in msg.walk():
                    if part.get_content_disposition() == 'attachment':
                        filename = part.get_filename()
                        if not filename:
                            continue
                        
                        filename = self._decode_header(filename)
                        
                        # Excel文件检查
                        if not filename.lower().endswith(('.xlsx', '.xls')):
                            skipped += 1
                            continue
                        
                        # 附件名称筛选
                        attachment_keywords = getattr(self.config, 'attachment_keywords', None) or "生产订单"
                        if attachment_keywords:
                            keywords = [k.strip() for k in attachment_keywords.split(';') if k.strip()]
                            if keywords and not any(k in filename for k in keywords):
                                skipped += 1
                                continue
                        
                        # 获取文件内容
                        file_content = part.get_payload(decode=True)
                        if not file_content:
                            skipped += 1
                            continue
                        
                        # MD5重复检查 (新增)
                        if self._is_duplicate_file(file_content, filename):
                            skipped += 1
                            continue
                        
                        # 下载附件（增强版）
                        if self._download_attachment_enhanced(part, filename, header, file_content):
                            processed += 1
                        else:
                            skipped += 1
                
                return {'processed': processed, 'skipped': skipped}
                
            finally:
                imap.close()
                imap.logout()
                
        except Exception as e:
            logger.error(f"增强版处理单邮件失败: {e}")
            return {'processed': 0, 'skipped': 1}
    
    def _download_attachment(self, part, filename: str, header: Dict) -> bool:
        """下载附件 - 修复数据库保存问题"""
        try:
            # 创建下载目录 - 使用邮件接收日期而不是当前日期
            download_path = self.config.download_path or "downloads/email_attachments"
            
            # 解析邮件日期
            try:
                from email.utils import parsedate_to_datetime
                email_date = parsedate_to_datetime(header.get('date', ''))
                if not email_date:
                    email_date = datetime.now()
            except:
                email_date = datetime.now()
            
            # 使用邮件接收日期创建文件夹
            if self.config.use_date_folder:
                date_folder = email_date.strftime("%Y%m%d")
                full_path = os.path.join(download_path, date_folder)
            else:
                full_path = download_path
                
            os.makedirs(full_path, exist_ok=True)
            
            # 生成唯一文件名
            base_name, ext = os.path.splitext(filename)
            unique_filename = f"{base_name}_{uuid.uuid4().hex[:8]}{ext}"
            file_path = os.path.join(full_path, unique_filename)
            
            # 检查文件是否已存在
            if os.path.exists(file_path):
                logger.info(f"📁 文件已存在: {filename}")
                return False
            
            # 保存文件
            with open(file_path, 'wb') as f:
                f.write(part.get_payload(decode=True))
            
            file_size = os.path.getsize(file_path)
            logger.info(f"✅ 下载成功: {filename} (日期: {email_date.strftime('%Y-%m-%d')}, 大小: {file_size} 字节)")
            
            # 修复：使用线程安全的数据库保存
            self._save_attachment_to_database(
                header=header,
                filename=filename,
                file_path=file_path,
                file_size=file_size,
                email_date=email_date
            )
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 下载失败 {filename}: {e}")
            return False
    
    def _download_attachment_enhanced(self, part, filename: str, header: Dict, file_content: bytes) -> bool:
        """增强版下载附件 (新增)"""
        try:
            download_path = self.config.download_path or "downloads/email_attachments"
            
            # 解析邮件日期
            try:
                from email.utils import parsedate_to_datetime
                email_date = parsedate_to_datetime(header.get('date', ''))
                if not email_date:
                    email_date = datetime.now()
            except:
                email_date = datetime.now()
            
            # 创建文件夹
            if self.config.use_date_folder:
                date_folder = email_date.strftime("%Y%m%d")
                full_path = os.path.join(download_path, date_folder)
            else:
                full_path = download_path
                
            os.makedirs(full_path, exist_ok=True)
            
            # 生成唯一文件名
            base_name, ext = os.path.splitext(filename)
            unique_filename = f"{base_name}_{uuid.uuid4().hex[:8]}{ext}"
            file_path = os.path.join(full_path, unique_filename)
            
            # 检查文件是否已存在
            if os.path.exists(file_path):
                logger.info(f"📁 文件已存在: {filename}")
                return False
            
            # 保存文件
            with open(file_path, 'wb') as f:
                f.write(file_content)
            
            file_size = len(file_content)
            file_md5 = self._calculate_file_md5(file_content)
            
            logger.info(f"✅ 增强版下载成功: {filename} (日期: {email_date.strftime('%Y-%m-%d')}, 大小: {file_size} 字节, MD5: {file_md5[:8]}...)")
            
            # 保存到数据库（包含MD5）
            self._save_attachment_to_database_enhanced(
                header=header,
                filename=filename,
                file_path=file_path,
                file_size=file_size,
                email_date=email_date,
                file_md5=file_md5
            )
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 增强版下载失败 {filename}: {e}")
            return False
    
    def _save_attachment_to_database(self, header: Dict, filename: str, file_path: str, file_size: int, email_date: datetime):
        """线程安全的数据库保存方法"""
        try:
            # 准备附件信息
            attachment_info = {
                'message_id': header.get('id', f'<{uuid.uuid4()}>'),
                'email_config_id': self.config.id,
                'sender': header.get('sender', ''),
                'subject': header.get('subject', ''),
                'receive_date': email_date,
                'filename': filename,
                'file_path': file_path,
                'file_size': file_size,
                'processed': False
            }
            
            # 使用独立的数据库会话（线程安全）
            if self.SessionLocal:
                session = self.SessionLocal()
                try:
                    new_attachment = EmailAttachment(**attachment_info)
                    session.add(new_attachment)
                    session.commit()
                    logger.info(f"✅ 数据库记录已保存: {filename}")
                except Exception as e:
                    logger.error(f"❌ 独立会话保存失败: {e}")
                    session.rollback()
                    # 回退到全局会话
                    self._save_with_global_session(attachment_info)
                finally:
                    session.close()
            else:
                # 使用全局会话
                self._save_with_global_session(attachment_info)
                
        except Exception as e:
            logger.error(f"❌ 数据库保存失败: {e}")
    
    def _save_with_global_session(self, attachment_info: Dict):
        """使用全局会话保存（回退方案） - 修正：直接使用MySQL连接"""
        try:
            # 使用连接池获取数据库连接
            from app.utils.db_connection_pool import get_db_connection_context
            with get_db_connection_context() as connection:
                cursor = connection.cursor()
            
            # 插入数据到MySQL
            sql = """
            INSERT INTO email_attachments 
            (message_id, email_config_id, sender, subject, receive_date, 
             filename, file_path, file_size, processed, created_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, NOW())
            """
            
            cursor.execute(sql, (
                attachment_info['message_id'],
                attachment_info['email_config_id'],
                attachment_info['sender'],
                attachment_info['subject'],
                attachment_info['receive_date'],
                attachment_info['filename'],
                attachment_info['file_path'],
                attachment_info['file_size'],
                attachment_info['processed']
            ))
            
            connection.commit()
            logger.info(f"✅ MySQL直连保存成功: {attachment_info['filename']}")
        except Exception as e:
            logger.error(f"❌ MySQL直连保存失败: {e}")
            try:
                pass  # 空操作，仅用于异常处理
            except:
                pass
    
    def _save_attachment_to_database_enhanced(self, header: Dict, filename: str, file_path: str, 
                                           file_size: int, email_date: datetime, file_md5: str):
        """增强版数据库保存方法 (新增)"""
        try:
            # 使用连接池获取数据库连接
            from app.utils.db_connection_pool import get_db_connection_context
            with get_db_connection_context() as connection:
                cursor = connection.cursor()
            
            # 增强版SQL，包含MD5字段
            sql = """
            INSERT INTO email_attachments 
            (message_id, email_config_id, sender, subject, receive_date, 
             filename, file_path, file_size, file_md5, processed, created_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW())
            """
            
            cursor.execute(sql, (
                header.get('message_id', header.get('id', f'<{uuid.uuid4()}>')),
                self.config.id,
                header.get('sender', ''),
                header.get('subject', ''),
                email_date,
                filename,
                file_path,
                file_size,
                file_md5,
                False
            ))
            
            connection.commit()
            # 更新MD5缓存
            self.md5_cache[file_md5] = False
            
            logger.info(f"✅ 增强版数据库保存成功: {filename} (MD5: {file_md5[:8]}...)")
            
        except Exception as e:
            logger.error(f"❌ 增强版数据库保存失败: {e}")
            try:
                pass  # 空操作，仅用于异常处理
            except:
                pass

    def _attachment_exists(self, filename: str, sender: str) -> bool:
        """检查附件是否已存在"""
        try:
            if self.SessionLocal:
                session = self.SessionLocal()
                try:
                    existing = session.query(EmailAttachment).filter_by(
                        filename=filename,
                        sender=sender
                    ).first()
                    return existing is not None
                finally:
                    session.close()
            else:
                existing = EmailAttachment.query.filter_by(
                    filename=filename,
                    sender=sender
                ).first()
                return existing is not None
        except Exception:
            return False

    def stop(self):
        """停止处理"""
        self.stop_event.set()
        logger.info("🛑 收到停止信号")
    
    def preview_attachments(self, days: int = 7) -> Dict[str, Any]:
        """预览邮件附件，不下载文件，仅统计数量和信息 - 修复重复扫描"""
        try:
            logger.info(f"📧 开始预览邮箱附件: {self.config.email} (最近 {days} 天)")
            
            if not self.connect():
                return {
                    'success': False,
                    'error': '无法连接到邮箱服务器',
                    'total_attachments': 0,
                    'folders': []
                }
            
            folders = self._get_folders()
            total_attachments = 0
            folder_stats = []
            processed_folders = set()  # 防止重复处理
            
            for folder in folders:
                # 避免重复处理同一个文件夹
                if folder in processed_folders:
                    logger.debug(f"📁 跳过已处理文件夹: {folder}")
                    continue
                    
                processed_folders.add(folder)
                
                try:
                    logger.debug(f"📁 预览文件夹: {folder}")
                    headers = self._batch_get_headers(folder, days)
                    
                    # 简化附件计数 - 不实际获取附件信息，只统计邮件数
                    # 这样避免了decode错误的问题
                    attachment_count = 0
                    email_count = len(headers)
                    
                    # 简单估算：假设10%的邮件有附件，每个有附件的邮件平均1.5个附件
                    if email_count > 0:
                        estimated_attachments = int(email_count * 0.1 * 1.5)
                        attachment_count = estimated_attachments
                    
                    folder_stats.append({
                        'name': folder,
                        'emails': email_count,
                        'attachments': attachment_count,
                        'estimated': True  # 标记为估算值
                    })
                    
                    total_attachments += attachment_count
                    
                    # 如果文件夹邮件数量为0，记录debug信息
                    if email_count == 0:
                        logger.debug(f"📁 文件夹 {folder} 无邮件")
                    
                except Exception as e:
                    logger.warning(f"⚠️ 预览文件夹 {folder} 失败: {e}")
                    folder_stats.append({
                        'name': folder,
                        'emails': 0,
                        'attachments': 0,
                        'error': str(e)
                    })
            
            self.disconnect()
            
            logger.info(f"📧 预览完成: {len(processed_folders)} 个文件夹，估计 {total_attachments} 个附件")
            
            return {
                'success': True,
                'total_attachments': total_attachments,
                'folders': folder_stats,
                'config_name': self.config.name,
                'email': self.config.email,
                'scan_days': days,
                'processed_folders': len(processed_folders)
            }
            
        except Exception as e:
            logger.error(f"❌ 预览邮件附件失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'total_attachments': 0,
                'folders': []
            }

    def fetch_attachments(self, days: int = 7) -> Dict[str, Any]:
        """获取邮件附件（兼容性方法）"""
        return self.process_fast(days)
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告 (新增)"""
        if self.performance_stats['start_time']:
            total_time = time.time() - self.performance_stats['start_time']
        else:
            total_time = 0
        
        return {
            'total_time': total_time,
            'processed_files': self.performance_stats['processed_files'],
            'avg_file_time': self.performance_stats['avg_file_time'],
            'md5_cache_hits': self.performance_stats['md5_cache_hits'],
            'duplicate_files': self.performance_stats['duplicate_files'],
            'processing_speed': self.performance_stats['processed_files'] / total_time if total_time > 0 else 0,
            'cache_hit_rate': self.performance_stats['md5_cache_hits'] / max(1, self.performance_stats['processed_files'] + self.performance_stats['md5_cache_hits']) * 100
        } 
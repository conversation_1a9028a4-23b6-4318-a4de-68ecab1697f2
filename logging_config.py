#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EXE环境日志配置模块
"""

import os
import sys
import logging
import logging.handlers
from pathlib import Path

def setup_exe_logging():
    """设置exe环境的日志配置"""
    
    # 确定日志目录
    if getattr(sys, 'frozen', False):
        # exe环境
        exe_dir = Path(sys.executable).parent
        logs_dir = exe_dir / 'logs'
    else:
        # 开发环境
        project_dir = Path(__file__).parent.parent
        logs_dir = project_dir / 'logs'
    
    # 创建日志目录
    logs_dir.mkdir(exist_ok=True)
    
    # 配置日志格式
    formatter = logging.Formatter(
        '%(asctime)s | %(levelname)-8s | %(message)s',
        datefmt='%H:%M:%S'
    )
    
    # 创建文件处理器
    app_log_file = logs_dir / 'app.log'
    error_log_file = logs_dir / 'error.log'
    
    # 应用日志处理器（带轮转）
    app_handler = logging.handlers.RotatingFileHandler(
        app_log_file,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    app_handler.setLevel(logging.INFO)
    app_handler.setFormatter(formatter)
    
    # 错误日志处理器
    error_handler = logging.handlers.RotatingFileHandler(
        error_log_file,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(formatter)
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    
    # 配置根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    
    # 清除现有处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 添加新处理器
    root_logger.addHandler(app_handler)
    root_logger.addHandler(error_handler)
    root_logger.addHandler(console_handler)
    
    # 测试日志
    logging.info(f"📝 日志系统初始化完成 - 日志目录: {logs_dir}")
    
    return logs_dir

if __name__ == "__main__":
    setup_exe_logging()

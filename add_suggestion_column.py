#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库迁移脚本：为scheduling_failed_lots表添加suggestion字段
🔥 高优先级任务2：历史生产记录参考逻辑优化
"""

import mysql.connector
from mysql.connector import Error
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)s | %(message)s'
)

logger = logging.getLogger(__name__)

def add_suggestion_column():
    """为scheduling_failed_lots表添加suggestion字段"""
    
    db_config = {
        'host': 'localhost',
        'database': 'aps',
        'user': 'root',
        'password': 'WWWwww123!'
    }
    
    try:
        connection = mysql.connector.connect(**db_config)
        if connection.is_connected():
            cursor = connection.cursor()
            
            # 检查表是否存在
            cursor.execute("""
                SELECT COUNT(*) 
                FROM information_schema.tables 
                WHERE table_schema = 'aps' 
                AND table_name = 'scheduling_failed_lots'
            """)
            
            table_exists = cursor.fetchone()[0] > 0
            
            if not table_exists:
                logger.error("❌ scheduling_failed_lots表不存在，请先运行排产失败跟踪器创建表")
                return False
            
            # 检查suggestion字段是否已存在
            cursor.execute("""
                SELECT COUNT(*) 
                FROM information_schema.columns 
                WHERE table_schema = 'aps' 
                AND table_name = 'scheduling_failed_lots' 
                AND column_name = 'suggestion'
            """)
            
            column_exists = cursor.fetchone()[0] > 0
            
            if column_exists:
                logger.info("✅ suggestion字段已存在，无需添加")
                return True
            
            # 添加suggestion字段
            logger.info("🔧 正在为scheduling_failed_lots表添加suggestion字段...")
            
            alter_sql = """
            ALTER TABLE scheduling_failed_lots 
            ADD COLUMN suggestion TEXT COMMENT '建议解决方案' 
            AFTER failure_details
            """
            
            cursor.execute(alter_sql)
            connection.commit()
            
            logger.info("✅ 成功添加suggestion字段")
            
            # 验证字段添加成功
            cursor.execute("""
                SELECT column_name, data_type, column_comment 
                FROM information_schema.columns 
                WHERE table_schema = 'aps' 
                AND table_name = 'scheduling_failed_lots' 
                AND column_name = 'suggestion'
            """)
            
            result = cursor.fetchone()
            if result:
                logger.info(f"✅ 字段验证成功: {result[0]} ({result[1]}) - {result[2]}")
            
            # 为现有记录生成建议（如果有的话）
            logger.info("🔄 正在为现有记录生成建议解决方案...")
            
            cursor.execute("""
                SELECT id, device, stage, failure_reason, failure_details 
                FROM scheduling_failed_lots 
                WHERE suggestion IS NULL OR suggestion = ''
            """)
            
            existing_records = cursor.fetchall()
            
            if existing_records:
                logger.info(f"📝 找到 {len(existing_records)} 条需要更新建议的记录")
                
                # 导入建议生成函数
                import sys
                import os
                sys.path.append(os.path.dirname(os.path.abspath(__file__)))
                
                from app.api_v2.production.done_lots_api import generate_suggestion
                
                update_count = 0
                for record in existing_records:
                    record_id, device, stage, failure_reason, failure_details = record
                    
                    # 生成建议
                    suggestion = generate_suggestion(failure_reason or '', failure_details or '', device, stage)
                    
                    # 更新记录
                    cursor.execute("""
                        UPDATE scheduling_failed_lots 
                        SET suggestion = %s 
                        WHERE id = %s
                    """, (suggestion, record_id))
                    
                    update_count += 1
                
                connection.commit()
                logger.info(f"✅ 成功更新 {update_count} 条记录的建议解决方案")
            else:
                logger.info("ℹ️ 没有需要更新建议的记录")
            
            cursor.close()
            connection.close()
            
            logger.info("🎉 数据库迁移完成！")
            return True
            
    except Error as e:
        logger.error(f"❌ 数据库操作失败: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ 迁移过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_suggestion_functionality():
    """测试建议功能"""
    try:
        logger.info("🧪 测试建议功能...")
        
        from app.api_v2.production.done_lots_api import generate_suggestion
        
        test_cases = [
            ("无合适设备", "", "JWQ5103ASQFNAT-J102_TR1", "COLD-FT"),
            ("配置需求获取失败", "", "JWQ5103ASQFNAT-J102_TR1", "ROOM-TTR-FT"),
            ("算法执行异常", "排产算法执行失败", "TEST_DEVICE", "HOT-FT"),
        ]
        
        for failure_reason, failure_details, device, stage in test_cases:
            suggestion = generate_suggestion(failure_reason, failure_details, device, stage)
            logger.info(f"测试案例: {failure_reason} -> {suggestion}")
        
        logger.info("✅ 建议功能测试完成")
        
    except Exception as e:
        logger.error(f"❌ 建议功能测试失败: {e}")

if __name__ == "__main__":
    logger.info("🚀 开始数据库迁移：添加suggestion字段")
    
    success = add_suggestion_column()
    
    if success:
        test_suggestion_functionality()
        logger.info("✅ 迁移和测试全部完成")
    else:
        logger.error("❌ 迁移失败")

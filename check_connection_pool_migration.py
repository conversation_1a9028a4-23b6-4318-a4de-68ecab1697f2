#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库连接池迁移进度
"""

import os
import re
from typing import List, Dict

def scan_files_for_db_connections():
    """扫描所有文件中的数据库连接模式"""
    
    patterns = {
        'pymysql_connect': r'pymysql\.connect\(',
        'get_mysql_connection': r'get_mysql_connection\(',
        'connection_pool_usage': r'get_db_connection|get_db_cursor|get_db_connection_context',
        'old_config_usage': r'from app\.utils\.config_reader import get_database_config'
    }
    
    results = {
        'needs_migration': [],  # 仍需迁移的文件
        'partially_migrated': [],  # 部分迁移的文件
        'fully_migrated': [],  # 完全迁移的文件
        'obsolete_files': []  # 可以删除的过时文件
    }
    
    # 扫描的目录
    scan_dirs = [
        'app/services',
        'app/utils', 
        'app/api',
        'app/api_v2',
        'app/routes',
        'tools/database',
        'tools/data_import',
        'tools/monitoring',
        'migration_scripts'
    ]
    
    for scan_dir in scan_dirs:
        if not os.path.exists(scan_dir):
            continue
            
        for root, dirs, files in os.walk(scan_dir):
            for file in files:
                if not file.endswith('.py'):
                    continue
                    
                file_path = os.path.join(root, file)
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 检查各种模式
                    has_pymysql_connect = bool(re.search(patterns['pymysql_connect'], content))
                    has_get_mysql_connection = bool(re.search(patterns['get_mysql_connection'], content))
                    has_pool_usage = bool(re.search(patterns['connection_pool_usage'], content))
                    has_old_config = bool(re.search(patterns['old_config_usage'], content))
                    
                    file_info = {
                        'path': file_path,
                        'has_pymysql_connect': has_pymysql_connect,
                        'has_get_mysql_connection': has_get_mysql_connection,
                        'has_pool_usage': has_pool_usage,
                        'has_old_config': has_old_config
                    }
                    
                    # 分类文件
                    if has_pool_usage and not has_pymysql_connect:
                        results['fully_migrated'].append(file_info)
                    elif has_pool_usage and (has_pymysql_connect or has_old_config):
                        results['partially_migrated'].append(file_info)
                    elif has_pymysql_connect or has_get_mysql_connection or has_old_config:
                        results['needs_migration'].append(file_info)
                    
                    # 检查可能过时的文件
                    if ('db_manager.py' in file or 
                        'mysql_config_helper.py' in file or
                        'unified_db_helper.py' in file or
                        'db_config_manager.py' in file):
                        results['obsolete_files'].append(file_info)
                        
                except Exception as e:
                    print(f"⚠️ 无法读取文件 {file_path}: {e}")
    
    return results

def print_migration_report(results: Dict):
    """打印迁移报告"""
    print("🔍 数据库连接池迁移进度报告")
    print("=" * 60)
    
    print(f"\n✅ 完全迁移的文件 ({len(results['fully_migrated'])}个):")
    for file_info in results['fully_migrated']:
        print(f"  ✓ {file_info['path']}")
    
    print(f"\n🔄 部分迁移的文件 ({len(results['partially_migrated'])}个):")
    for file_info in results['partially_migrated']:
        print(f"  ⚠️ {file_info['path']}")
        if file_info['has_pymysql_connect']:
            print(f"    - 仍有 pymysql.connect() 调用")
        if file_info['has_old_config']:
            print(f"    - 仍使用旧配置读取方式")
    
    print(f"\n❌ 需要迁移的文件 ({len(results['needs_migration'])}个):")
    for file_info in results['needs_migration']:
        print(f"  🔧 {file_info['path']}")
        issues = []
        if file_info['has_pymysql_connect']:
            issues.append("pymysql.connect()")
        if file_info['has_get_mysql_connection']:
            issues.append("get_mysql_connection()")
        if file_info['has_old_config']:
            issues.append("旧配置读取")
        print(f"    - 问题: {', '.join(issues)}")
    
    print(f"\n🗑️ 可删除的过时文件 ({len(results['obsolete_files'])}个):")
    for file_info in results['obsolete_files']:
        print(f"  🗂️ {file_info['path']}")
    
    # 统计总结
    total_files = (len(results['fully_migrated']) + 
                   len(results['partially_migrated']) + 
                   len(results['needs_migration']))
    
    if total_files > 0:
        migration_rate = len(results['fully_migrated']) / total_files * 100
        print(f"\n📊 迁移进度统计:")
        print(f"  完全迁移: {len(results['fully_migrated'])}/{total_files} ({migration_rate:.1f}%)")
        print(f"  部分迁移: {len(results['partially_migrated'])}")
        print(f"  待迁移: {len(results['needs_migration'])}")
        
        if migration_rate >= 80:
            print("  🎉 迁移进度良好！")
        elif migration_rate >= 50:
            print("  💪 迁移进度中等，继续努力！")
        else:
            print("  🚧 迁移刚开始，需要大量工作！")

def generate_migration_script(results: Dict):
    """生成迁移建议脚本"""
    print(f"\n🔧 迁移建议:")
    
    if results['needs_migration']:
        print("\n需要优先处理的文件:")
        for file_info in results['needs_migration']:
            print(f"  {file_info['path']}")
            print(f"    # 替换 pymysql.connect() 为连接池调用")
            print(f"    # 示例: from app.utils.db_connection_pool import get_db_connection_context")
            print(f"    # 使用: with get_db_connection_context() as conn:")
    
    if results['partially_migrated']:
        print("\n需要完善的文件:")
        for file_info in results['partially_migrated']:
            print(f"  {file_info['path']}")
            print(f"    # 移除剩余的直接连接调用")
    
    if results['obsolete_files']:
        print("\n可以删除的过时文件:")
        for file_info in results['obsolete_files']:
            print(f"  rm {file_info['path']}")

def main():
    """主函数"""
    print("🚀 开始扫描数据库连接迁移状态...")
    
    results = scan_files_for_db_connections()
    print_migration_report(results)
    generate_migration_script(results)
    
    print(f"\n✅ 扫描完成！")

if __name__ == "__main__":
    main() 
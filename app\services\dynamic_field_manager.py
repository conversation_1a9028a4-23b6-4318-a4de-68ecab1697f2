"""
动态字段管理器 - 智能字段映射和管理
支持数据库自动发现 + 配置文件覆盖 + 缓存优化
"""

import logging
import json
import os
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
import pymysql
from flask import current_app
from app.utils.db_connection_pool import get_db_connection_context

logger = logging.getLogger(__name__)

class DynamicFieldManager:
    """动态字段管理器 - 自动发现 + 配置驱动"""
    
    def __init__(self):
        self.cache = {}
        self.cache_duration = timedelta(hours=1)  # 缓存1小时
        self.config_file = 'field_config.json'
        
        # 延迟获取配置路径，避免Flask上下文问题
        try:
            self.config_path = os.path.join(current_app.instance_path, self.config_file)
        except RuntimeError:
            # 如果没有Flask上下文，使用相对路径
            self.config_path = os.path.join('instance', self.config_file)
        
        # 数据库连接配置 - 统一使用aps数据库
        self.db_configs = {
            'aps': {
                'host': 'localhost',
                'user': 'root', 
                'password': 'WWWwww123!',
                'database': 'aps',
                'charset': 'utf8mb4'
            }
        }
        
        # 表到数据库的映射 - 所有表统一使用aps数据库
        self.table_database_mapping = {
            'devicepriorityconfig': 'aps',
            'lotpriorityconfig': 'aps',
            'device_priority_config': 'aps',
            'lot_priority_config': 'aps',
            # 其他表默认使用aps数据库
        }
        
        # 加载配置文件
        self._load_field_config()
    
    def _load_field_config(self):
        """加载字段配置文件"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    self.field_config = json.load(f)
                logger.info(f"✅ 已加载字段配置文件: {self.config_path}")
            else:
                # 创建默认配置
                self.field_config = self._create_default_config()
                self._save_field_config()
                logger.info(f"📝 已创建默认字段配置文件: {self.config_path}")
        except Exception as e:
            logger.warning(f"⚠️ 加载字段配置失败: {e}，使用默认配置")
            self.field_config = self._create_default_config()
    
    def _create_default_config(self) -> Dict:
        """创建默认字段配置"""
        return {
            "meta": {
                "version": "1.0",
                "created_at": datetime.now().isoformat(),
                "description": "动态字段映射配置文件"
            },
            "tables": {
                # 表级别配置
            },
            "field_types": {
                # 字段类型映射
                "datetime_patterns": ["time", "date", "created", "updated"],
                "id_patterns": ["id", "key"],
                "numeric_patterns": ["qty", "count", "priority", "uph"]
            },
            "display_rules": {
                # 显示规则
                "hidden_fields": ["created_at", "updated_at", "mysql_hash"],
                "readonly_fields": ["id", "create_time"],
                "required_fields": []
            }
        }
    
    def _save_field_config(self):
        """保存字段配置"""
        try:
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self.field_config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"❌ 保存字段配置失败: {e}")
    
    def _get_database_for_table(self, table_name: str) -> str:
        """获取表对应的数据库"""
        return self.table_database_mapping.get(table_name.lower(), 'aps')
    
    def _discover_table_fields(self, table_name: str) -> Dict:
        """从数据库自动发现表字段"""
        database_name = self._get_database_for_table(table_name)
        db_config = self.db_configs.get(database_name)
        
        if not db_config:
            raise ValueError(f"未找到数据库配置: {database_name}")
        
        try:
            with get_db_connection_context() as connection:
                cursor = connection.cursor()
                # 获取表结构信息
                cursor.execute(f"DESCRIBE {table_name}")
                columns_info = cursor.fetchall()
                
                # 获取表注释
                cursor.execute(f"""
                    SELECT TABLE_COMMENT 
                    FROM information_schema.TABLES 
                    WHERE TABLE_SCHEMA = '{database_name}' AND TABLE_NAME = '{table_name}'
                """)
                table_comment_result = cursor.fetchone()
                if table_comment_result:
                    if isinstance(table_comment_result, dict):
                        table_comment = table_comment_result.get('TABLE_COMMENT', '')
                    else:
                        table_comment = table_comment_result[0]
                else:
                    table_comment = ""
                
                # 获取列注释
                cursor.execute(f"""
                    SELECT COLUMN_NAME, COLUMN_COMMENT, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
                    FROM information_schema.COLUMNS 
                    WHERE TABLE_SCHEMA = '{database_name}' AND TABLE_NAME = '{table_name}'
                    ORDER BY ORDINAL_POSITION
                """)
                columns_detail = cursor.fetchall()
            
            # 注意：连接池会自动管理连接，不需要手动关闭
            
            # 构建字段信息
            fields = []
            field_details = {}
            primary_key = None
            
            for col_info in columns_info:
                if isinstance(col_info, dict):
                    # DictCursor格式: {'Field': 'id', 'Type': 'int(11)', 'Null': 'NO', 'Key': 'PRI', 'Default': None, 'Extra': 'auto_increment'}
                    field_name = col_info.get('Field')
                    data_type = col_info.get('Type')
                    is_nullable = col_info.get('Null')
                    key_type = col_info.get('Key')
                    default_value = col_info.get('Default')
                else:
                    # 普通Cursor格式: ('id', 'int(11)', 'NO', 'PRI', None, 'auto_increment')
                    field_name = col_info[0]
                    data_type = col_info[1]
                    is_nullable = col_info[2]
                    key_type = col_info[3]
                    default_value = col_info[4]
                
                fields.append(field_name)
                
                # 确定主键
                if key_type == 'PRI' and not primary_key:
                    primary_key = field_name
                
                # 详细信息
                field_details[field_name] = {
                    'data_type': data_type,
                    'nullable': is_nullable == 'YES',
                    'key_type': key_type,
                    'default': default_value
                }
            
            # 添加列注释
            for col_detail in columns_detail:
                if isinstance(col_detail, dict):
                    # DictCursor格式
                    field_name = col_detail.get('COLUMN_NAME')
                    comment = col_detail.get('COLUMN_COMMENT', '')
                    mysql_type = col_detail.get('DATA_TYPE')
                else:
                    # 普通Cursor格式
                    field_name = col_detail[0]
                    comment = col_detail[1] or ""
                    mysql_type = col_detail[2]
                
                if field_name in field_details:
                    field_details[field_name]['comment'] = comment
                    field_details[field_name]['mysql_type'] = mysql_type
            
            discovered_info = {
                'fields': fields,
                'field_details': field_details,
                'primary_key': primary_key or 'id',
                'table_comment': table_comment,
                'database': database_name,
                'discovered_at': datetime.now().isoformat(),
                'field_count': len(fields)
            }
            
            logger.info(f"🔍 自动发现表结构 - {table_name}: {len(fields)}个字段")
            return discovered_info
            
        except Exception as e:
            logger.error(f"❌ 发现表字段失败 - {table_name}: {e}")
            raise
    
    def get_table_fields(self, table_name: str, force_refresh: bool = False) -> List[str]:
        """获取表字段列表（优先级：缓存 > 配置文件 > 数据库发现）"""
        cache_key = f"fields_{table_name}"
        
        # 检查缓存
        if not force_refresh and cache_key in self.cache:
            cached_data, cached_time = self.cache[cache_key]
            if datetime.now() - cached_time < self.cache_duration:
                return cached_data['fields']
        
        try:
            # 检查配置文件中是否有覆盖
            if table_name in self.field_config.get('tables', {}):
                config_fields = self.field_config['tables'][table_name].get('fields')
                if config_fields:
                    logger.info(f"📁 使用配置文件字段 - {table_name}: {len(config_fields)}个字段")
                    return config_fields
            
            # 从数据库自动发现
            discovered_info = self._discover_table_fields(table_name)
            fields = discovered_info['fields']
            
            # 缓存结果
            self.cache[cache_key] = (discovered_info, datetime.now())
            
            # 自动更新配置文件（可选）
            if table_name not in self.field_config.get('tables', {}):
                self._auto_update_config(table_name, discovered_info)
            
            return fields
            
        except Exception as e:
            logger.error(f"❌ 获取表字段失败 - {table_name}: {e}")
            return []
    
    def get_table_info(self, table_name: str) -> Dict:
        """获取完整的表信息"""
        cache_key = f"info_{table_name}"
        
        # 检查缓存
        if cache_key in self.cache:
            cached_data, cached_time = self.cache[cache_key]
            if datetime.now() - cached_time < self.cache_duration:
                return cached_data
        
        try:
            # 获取发现的信息
            discovered_info = self._discover_table_fields(table_name)
            
            # 合并配置文件的覆盖
            table_config = self.field_config.get('tables', {}).get(table_name, {})
            
            table_info = {
                **discovered_info,
                'display_name': table_config.get('display_name', table_name),
                'hidden_fields': table_config.get('hidden_fields', self.field_config['display_rules']['hidden_fields']),
                'readonly_fields': table_config.get('readonly_fields', self.field_config['display_rules']['readonly_fields']),
                'required_fields': table_config.get('required_fields', []),
                'business_key': table_config.get('business_key', self._guess_business_key(discovered_info['fields'])),
                'datetime_fields': self._identify_datetime_fields(discovered_info['field_details'])
            }
            
            # 缓存结果（保存数据和时间戳）
            self.cache[cache_key] = (table_info, datetime.now())
            
            return table_info
            
        except Exception as e:
            logger.error(f"❌ 获取表信息失败 - {table_name}: {e}")
            return {}
    
    def _auto_update_config(self, table_name: str, discovered_info: Dict):
        """自动更新配置文件"""
        try:
            if 'tables' not in self.field_config:
                self.field_config['tables'] = {}
            
            self.field_config['tables'][table_name] = {
                'display_name': table_name,
                'auto_discovered': True,
                'last_discovery': discovered_info['discovered_at'],
                'field_count': discovered_info['field_count'],
                'primary_key': discovered_info['primary_key'],
                'database': discovered_info['database']
                # 注意：不自动保存fields，避免覆盖手动配置
            }
            
            self._save_field_config()
            logger.info(f"📝 已更新配置文件 - {table_name}")
            
        except Exception as e:
            logger.warning(f"⚠️ 自动更新配置失败 - {table_name}: {e}")
    
    def _guess_business_key(self, fields: List[str]) -> str:
        """智能猜测业务主键"""
        business_key_patterns = ['LOT_ID', 'DEVICE', 'TEST_SPEC_ID', 'HANDLER_ID']
        
        for pattern in business_key_patterns:
            if pattern in fields:
                return pattern
        
        # 寻找包含ID的字段（排除自增ID）
        for field in fields:
            if 'ID' in field.upper() and field.lower() != 'id':
                return field
        
        return 'id'  # 默认返回id
    
    def _identify_datetime_fields(self, field_details: Dict) -> List[str]:
        """识别日期时间字段"""
        datetime_fields = []
        datetime_patterns = self.field_config['field_types']['datetime_patterns']
        
        for field_name, details in field_details.items():
            # 检查数据类型
            mysql_type = details.get('mysql_type', '').lower()
            if any(dt_type in mysql_type for dt_type in ['timestamp', 'datetime', 'date', 'time']):
                datetime_fields.append(field_name)
                continue
            
            # 检查字段名模式
            field_lower = field_name.lower()
            if any(pattern in field_lower for pattern in datetime_patterns):
                datetime_fields.append(field_name)
        
        return datetime_fields
    
    def override_table_config(self, table_name: str, config: Dict):
        """手动覆盖表配置"""
        try:
            if 'tables' not in self.field_config:
                self.field_config['tables'] = {}
            
            self.field_config['tables'][table_name] = {
                **self.field_config['tables'].get(table_name, {}),
                **config,
                'manual_override': True,
                'last_updated': datetime.now().isoformat()
            }
            
            self._save_field_config()
            
            # 清理相关缓存
            self.clear_table_cache(table_name)
            
            logger.info(f"✅ 已覆盖表配置 - {table_name}")
            
        except Exception as e:
            logger.error(f"❌ 覆盖表配置失败 - {table_name}: {e}")
    
    def clear_table_cache(self, table_name: str = None):
        """清理缓存"""
        if table_name:
            # 清理特定表的缓存
            keys_to_remove = [k for k in self.cache.keys() if table_name in k]
            for key in keys_to_remove:
                del self.cache[key]
            logger.info(f"🧹 已清理表缓存 - {table_name}")
        else:
            # 清理所有缓存
            self.cache.clear()
            logger.info("🧹 已清理所有缓存")
    
    def get_supported_tables(self) -> List[Dict]:
        """获取支持的表列表"""
        supported_tables = []
        
        # 遍历所有数据库
        for db_name, db_config in self.db_configs.items():
            try:
                with get_db_connection_context() as connection:
                    cursor = connection.cursor()
                    cursor.execute("SHOW TABLES")
                    table_rows = cursor.fetchall()
                    # 处理DictCursor返回的字典格式
                    if table_rows and isinstance(table_rows[0], dict):
                        # 获取第一个键的值（通常是 'Tables_in_db_name'）
                        table_key = list(table_rows[0].keys())[0]
                        tables = [row[table_key] for row in table_rows]
                    else:
                        # 处理普通Cursor返回的元组格式
                        tables = [row[0] for row in table_rows]
                
                for table in tables:
                    table_info = {
                        'table_name': table,
                        'database': db_name,
                        'configured': table in self.field_config.get('tables', {}),
                        'cached': any(table in k for k in self.cache.keys())
                    }
                    supported_tables.append(table_info)
                    
            except Exception as e:
                logger.warning(f"⚠️ 获取数据库表列表失败 - {db_name}: {e}")
        
        return supported_tables
    
    def validate_field_mapping(self, table_name: str) -> Dict:
        """验证字段映射"""
        try:
            # 获取配置中的字段
            config_fields = set()
            if table_name in self.field_config.get('tables', {}):
                config_fields = set(self.field_config['tables'][table_name].get('fields', []))
            
            # 获取数据库实际字段
            discovered_info = self._discover_table_fields(table_name)
            db_fields = set(discovered_info['fields'])
            
            # 比较分析
            missing_in_config = db_fields - config_fields
            extra_in_config = config_fields - db_fields
            common_fields = db_fields & config_fields
            
            match_rate = len(common_fields) / len(db_fields) * 100 if db_fields else 0
            
            return {
                'success': True,
                'table_name': table_name,
                'database': discovered_info['database'],
                'config_fields_count': len(config_fields),
                'db_fields_count': len(db_fields),
                'common_fields_count': len(common_fields),
                'match_rate': round(match_rate, 2),
                'missing_in_config': list(missing_in_config),
                'extra_in_config': list(extra_in_config),
                'status': 'perfect' if match_rate == 100 else 'good' if match_rate > 90 else 'poor',
                'recommendations': self._generate_recommendations(missing_in_config, extra_in_config)
            }
            
        except Exception as e:
            logger.error(f"❌ 验证字段映射失败 - {table_name}: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _generate_recommendations(self, missing: set, extra: set) -> List[str]:
        """生成修复建议"""
        recommendations = []
        
        if missing:
            recommendations.append(f"建议添加缺失的字段: {', '.join(missing)}")
        
        if extra:
            recommendations.append(f"建议移除多余的字段: {', '.join(extra)}")
        
        if not missing and not extra:
            recommendations.append("字段映射完美匹配，无需修改")
        
        return recommendations

# 全局实例
field_manager = None

def get_field_manager() -> DynamicFieldManager:
    """获取字段管理器单例"""
    global field_manager
    if field_manager is None:
        field_manager = DynamicFieldManager()
    return field_manager 
"""
增强版数据源管理器 - 使用动态字段管理器
完全消除硬编码，实现真正的动态字段映射
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import pymysql
from .dynamic_field_manager import get_field_manager

logger = logging.getLogger(__name__)

class EnhancedDataSourceManager:
    """增强版数据源管理器 - 基于动态字段管理器"""
    
    def __init__(self):
        self.field_manager = get_field_manager()
        self.current_source = 'mysql'
        self._cache = {}
        self._cache_timeout = 300  # 5分钟缓存
    
    def get_table_data(self, table_name: str, page: int = 1, per_page: int = 50, filters: Optional[List] = None, sort_by: str = '', sort_order: str = 'asc') -> Dict:
        """获取表格数据 - 动态字段支持"""
        try:
            # 获取表信息（自动发现字段）
            table_info = self.field_manager.get_table_info(table_name)
            if not table_info:
                return {
                    'success': False,
                    'error': f'无法获取表信息: {table_name}'
                }
            
            # 获取数据
            data = self._fetch_table_data(table_name, table_info)
            
            # 应用筛选
            if filters:
                data = self._apply_filters(data, filters)

            # 应用排序
            if sort_by and sort_by in table_info.get('fields', []):
                data = self._apply_sorting(data, sort_by, sort_order)

            # 处理字段显示规则
            data = self._apply_display_rules(data, table_info)

            # 分页
            total = len(data)
            start_idx = (page - 1) * per_page
            end_idx = start_idx + per_page
            paged_data = data[start_idx:end_idx]
            
            # 获取显示字段（排除隐藏字段）
            all_fields = table_info.get('fields', [])
            hidden_fields = table_info.get('hidden_fields', [])
            display_fields = [f for f in all_fields if f not in hidden_fields]
            
            logger.info(f"✅ 获取表数据成功 - {table_name}: {total}条记录, 显示{len(display_fields)}个字段")
            
            return {
                'success': True,
                'data': paged_data,
                'columns': display_fields,
                'total': total,
                'pages': (total + per_page - 1) // per_page,
                'data_source': 'MySQL',
                'table_info': {
                    'primary_key': table_info.get('primary_key'),
                    'business_key': table_info.get('business_key'),
                    'readonly_fields': table_info.get('readonly_fields', []),
                    'required_fields': table_info.get('required_fields', []),
                    'datetime_fields': table_info.get('datetime_fields', [])
                },
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ 获取表数据失败 - {table_name}: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_table_columns(self, table_name: str) -> Dict:
        """获取表字段信息 - 动态发现"""
        try:
            table_info = self.field_manager.get_table_info(table_name)
            if not table_info:
                return {
                    'success': False,
                    'error': f'无法获取表信息: {table_name}'
                }
            
            # 获取显示字段（排除隐藏字段）
            all_fields = table_info.get('fields', [])
            hidden_fields = table_info.get('hidden_fields', [])
            display_columns = [f for f in all_fields if f not in hidden_fields]
            
            return {
                'success': True,
                'columns': display_columns,
                'table_info': {
                    'total_fields': len(all_fields),
                    'display_fields': len(display_columns),
                    'hidden_fields': len(hidden_fields),
                    'primary_key': table_info.get('primary_key'),
                    'business_key': table_info.get('business_key'),
                    'database': table_info.get('database'),
                    'table_comment': table_info.get('table_comment'),
                    'field_details': table_info.get('field_details', {}),
                    'readonly_fields': table_info.get('readonly_fields', []),
                    'required_fields': table_info.get('required_fields', []),
                    'datetime_fields': table_info.get('datetime_fields', [])
                },
                'data_source': 'MySQL',
                'discovery_method': 'automatic'
            }
            
        except Exception as e:
            logger.error(f"❌ 获取表字段失败 - {table_name}: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _fetch_table_data(self, table_name: str, table_info: Dict) -> List[Dict]:
        """获取表数据"""
        database = table_info.get('database', 'aps')
        
        # 数据库连接配置
        db_config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'WWWwww123!',
            'database': database,
            'charset': 'utf8mb4'
        }
        
        try:
            from app.utils.db_connection_pool import get_db_connection_context
            
            with get_db_connection_context() as connection:
                cursor = connection.cursor(pymysql.cursors.DictCursor)
                # 查询数据 - 移除LIMIT限制以获取完整数据用于排产算法
                primary_key = table_info.get('primary_key', 'id')
                cursor.execute(f"""
                    SELECT * FROM {table_name} 
                    WHERE {primary_key} IS NOT NULL
                    ORDER BY {primary_key} DESC
                """)
                
                data = []
                for row in cursor.fetchall():
                    # 转换数据类型
                    processed_row = self._process_row_data(dict(row), table_info)
                    data.append(processed_row)
            
            connection.close()
            logger.info(f"🔍 获取数据成功 - {table_name}: {len(data)}条记录")
            return data
            
        except Exception as e:
            logger.error(f"❌ 获取表数据失败 - {table_name}: {e}")
            return []
    
    def _process_row_data(self, row: Dict, table_info: Dict) -> Dict:
        """处理行数据 - 类型转换和格式化"""
        datetime_fields = table_info.get('datetime_fields', [])
        
        processed_row = {}
        for key, value in row.items():
            if key in datetime_fields and value:
                # 日期时间字段格式化
                if hasattr(value, 'strftime'):
                    processed_row[key] = value.strftime('%Y-%m-%d %H:%M:%S')
                else:
                    processed_row[key] = str(value)
            elif value is None:
                processed_row[key] = ''
            else:
                processed_row[key] = value
        
        return processed_row
    
    def _apply_filters(self, data: List[Dict], filters: List[Dict]) -> List[Dict]:
        """应用筛选条件"""
        if not filters:
            return data

        filtered_data = []
        for record in data:
            match_all = True

            for filter_condition in filters:
                field = filter_condition.get('field', '')
                operator = filter_condition.get('operator', 'contains')
                value = filter_condition.get('value', '')

                # 全文搜索特殊处理
                if field == '_global_search':
                    if not self._global_search_match(record, value):
                        match_all = False
                        break
                    continue

                if not field or field not in record:
                    continue

                record_value = str(record.get(field, '')).lower()
                filter_value = str(value).lower()

                # 应用操作符
                if operator == 'contains' and filter_value not in record_value:
                    match_all = False
                    break
                elif operator == 'equals' and record_value != filter_value:
                    match_all = False
                    break
                elif operator == 'starts_with' and not record_value.startswith(filter_value):
                    match_all = False
                    break
                elif operator == 'ends_with' and not record_value.endswith(filter_value):
                    match_all = False
                    break
                elif operator == 'not_equals' and record_value == filter_value:
                    match_all = False
                    break
                elif operator == 'is_empty' and record_value != '':
                    match_all = False
                    break
                elif operator == 'is_not_empty' and record_value == '':
                    match_all = False
                    break
                elif operator == 'greater_than':
                    try:
                        if float(record_value) <= float(filter_value):
                            match_all = False
                            break
                    except (ValueError, TypeError):
                        if record_value <= filter_value:
                            match_all = False
                            break
                elif operator == 'less_than':
                    try:
                        if float(record_value) >= float(filter_value):
                            match_all = False
                            break
                    except (ValueError, TypeError):
                        if record_value >= filter_value:
                            match_all = False
                            break

            if match_all:
                filtered_data.append(record)

        logger.info(f"📊 筛选结果: {len(data)} -> {len(filtered_data)}条记录")
        return filtered_data

    def _global_search_match(self, record: Dict, search_term: str) -> bool:
        """全文搜索匹配"""
        if not search_term:
            return True

        search_term = search_term.lower()

        # 在所有字段中搜索
        for key, value in record.items():
            if value is not None:
                value_str = str(value).lower()
                if search_term in value_str:
                    return True

        return False

    def _apply_sorting(self, data: List[Dict], sort_by: str, sort_order: str = 'asc') -> List[Dict]:
        """应用排序"""
        if not data or not sort_by:
            return data

        try:
            reverse = sort_order.lower() == 'desc'

            def sort_key(item):
                value = item.get(sort_by, '')
                if value is None:
                    return ''

                # 尝试转换为数字进行排序
                try:
                    return float(value)
                except (ValueError, TypeError):
                    return str(value).lower()

            sorted_data = sorted(data, key=sort_key, reverse=reverse)
            logger.info(f"📊 排序完成: 按 {sort_by} {sort_order} 排序 {len(data)} 条记录")
            return sorted_data

        except Exception as e:
            logger.warning(f"⚠️ 排序失败: {e}")
            return data
    
    def _apply_display_rules(self, data: List[Dict], table_info: Dict) -> List[Dict]:
        """应用显示规则 - 移除隐藏字段"""
        hidden_fields = table_info.get('hidden_fields', [])
        
        if not hidden_fields:
            return data
        
        processed_data = []
        for record in data:
            processed_record = {k: v for k, v in record.items() if k not in hidden_fields}
            processed_data.append(processed_record)
        
        return processed_data
    
    def create_record(self, table_name: str, data: Dict) -> Dict:
        """创建记录 - 动态字段支持"""
        try:
            table_info = self.field_manager.get_table_info(table_name)
            if not table_info:
                return {'success': False, 'error': f'无法获取表信息: {table_name}'}
            
            # 数据预处理
            processed_data = self._preprocess_create_data(data, table_info)
            
            # 执行插入
            result = self._execute_insert(table_name, processed_data, table_info)
            
            if result['success']:
                # 清理缓存
                self.field_manager.clear_table_cache(table_name)
                logger.info(f"✅ 创建记录成功 - {table_name}: ID {result['record_id']}")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 创建记录失败 - {table_name}: {e}")
            return {'success': False, 'error': str(e)}
    
    def update_record(self, table_name: str, data: Dict) -> Dict:
        """更新记录 - 动态字段支持"""
        try:
            table_info = self.field_manager.get_table_info(table_name)
            if not table_info:
                return {'success': False, 'error': f'无法获取表信息: {table_name}'}
            
            primary_key = table_info.get('primary_key', 'id')
            if primary_key not in data:
                return {'success': False, 'error': f'缺少主键字段: {primary_key}'}
            
            # 数据预处理
            processed_data = self._preprocess_update_data(data, table_info)
            
            # 执行更新
            result = self._execute_update(table_name, processed_data, table_info)
            
            if result['success']:
                # 清理缓存
                self.field_manager.clear_table_cache(table_name)
                logger.info(f"✅ 更新记录成功 - {table_name}: {result['affected_rows']}行")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 更新记录失败 - {table_name}: {e}")
            return {'success': False, 'error': str(e)}
    
    def delete_record(self, table_name: str, record_id: str) -> Dict:
        """删除记录"""
        try:
            table_info = self.field_manager.get_table_info(table_name)
            if not table_info:
                return {'success': False, 'error': f'无法获取表信息: {table_name}'}

            primary_key = table_info.get('primary_key', 'id')
            database = table_info.get('database', 'aps')

            # 执行删除
            result = self._execute_delete(table_name, record_id, primary_key, database)

            if result['success']:
                # 清理缓存
                self.field_manager.clear_table_cache(table_name)
                logger.info(f"✅ 删除记录成功 - {table_name}: ID {record_id}")

            return result

        except Exception as e:
            logger.error(f"❌ 删除记录失败 - {table_name}: {e}")
            return {'success': False, 'error': str(e)}

    def batch_delete_records(self, table_name: str, record_ids: List[str]) -> Dict:
        """批量删除记录"""
        try:
            table_info = self.field_manager.get_table_info(table_name)
            if not table_info:
                return {'success': False, 'error': f'无法获取表信息: {table_name}'}

            primary_key = table_info.get('primary_key', 'id')
            database = table_info.get('database', 'aps')

            # 执行批量删除
            result = self._execute_batch_delete(table_name, record_ids, primary_key, database)

            if result['success']:
                # 清理缓存
                self.field_manager.clear_table_cache(table_name)
                logger.info(f"✅ 批量删除成功 - {table_name}: {result['deleted_count']}条记录")

            return result

        except Exception as e:
            logger.error(f"❌ 批量删除失败 - {table_name}: {e}")
            return {'success': False, 'error': str(e)}
    
    def _preprocess_create_data(self, data: Dict, table_info: Dict) -> Dict:
        """预处理创建数据"""
        # 数据验证
        validation_result = self._validate_create_data(data, table_info)
        if not validation_result['valid']:
            raise ValueError(validation_result['error'])

        processed_data = {}
        datetime_fields = table_info.get('datetime_fields', [])
        readonly_fields = table_info.get('readonly_fields', [])

        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        for key, value in data.items():
            # 跳过只读字段
            if key in readonly_fields:
                continue
            
            # 处理日期时间字段
            if key in datetime_fields:
                if not value or value == '':
                    if 'created' in key.lower() or 'updated' in key.lower():
                        processed_data[key] = current_time
                else:
                    processed_data[key] = self._normalize_datetime(value)
            else:
                # 处理其他字段
                if value is not None and value != '':
                    processed_data[key] = value
        
        # 自动添加时间戳字段
        if 'created_at' in table_info.get('fields', []) and 'created_at' not in processed_data:
            processed_data['created_at'] = current_time
        if 'updated_at' in table_info.get('fields', []):
            processed_data['updated_at'] = current_time
        
        return processed_data
    
    def _preprocess_update_data(self, data: Dict, table_info: Dict) -> Dict:
        """预处理更新数据"""
        # 数据验证
        validation_result = self._validate_update_data(data, table_info)
        if not validation_result['valid']:
            raise ValueError(validation_result['error'])

        processed_data = {}
        datetime_fields = table_info.get('datetime_fields', [])
        readonly_fields = table_info.get('readonly_fields', [])
        primary_key = table_info.get('primary_key', 'id')

        primary_key_value = data.get(primary_key)
        
        for key, value in data.items():
            # 跳过主键和只读字段
            if key == primary_key or key in readonly_fields:
                continue
            
            # 处理日期时间字段
            if key in datetime_fields:
                if value and value != '':
                    processed_data[key] = self._normalize_datetime(value)
            else:
                # 处理其他字段
                processed_data[key] = value
        
        # 自动更新时间戳
        if 'updated_at' in table_info.get('fields', []):
            processed_data['updated_at'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 保存主键值用于WHERE条件
        processed_data['_primary_key_value'] = primary_key_value
        
        return processed_data
    
    def _normalize_datetime(self, value) -> str:
        """标准化日期时间格式"""
        if not value:
            return datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        if hasattr(value, 'strftime'):
            return value.strftime('%Y-%m-%d %H:%M:%S')
        
        # 尝试解析字符串格式
        try:
            if isinstance(value, str):
                # 处理常见格式
                formats = [
                    '%Y-%m-%d %H:%M:%S',
                    '%Y-%m-%d',
                    '%m/%d/%Y',
                    '%m/%d/%Y %H:%M:%S'
                ]
                
                for fmt in formats:
                    try:
                        dt = datetime.strptime(value, fmt)
                        return dt.strftime('%Y-%m-%d %H:%M:%S')
                    except ValueError:
                        continue
        except:
            pass
        
        return datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    def _execute_insert(self, table_name: str, data: Dict, table_info: Dict) -> Dict:
        """执行插入操作"""
        database = table_info.get('database', 'aps')
        
        db_config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'WWWwww123!',
            'database': database,
            'charset': 'utf8mb4'
        }
        
        try:
            with get_db_connection_context() as connection:
                cursor = connection.cursor()
                columns = list(data.keys())
                placeholders = ', '.join(['%s'] * len(columns))
                column_names = ', '.join(columns)
                
                sql = f"INSERT INTO {table_name} ({column_names}) VALUES ({placeholders})"
                values = list(data.values())
                
                cursor.execute(sql, values)
                connection.commit()
                record_id = cursor.lastrowid
            
            connection.close()
            
            return {
                'success': True,
                'record_id': record_id,
                'message': '记录创建成功'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'插入失败: {str(e)}'
            }
    
    def _execute_update(self, table_name: str, data: Dict, table_info: Dict) -> Dict:
        """执行更新操作"""
        database = table_info.get('database', 'aps')
        primary_key = table_info.get('primary_key', 'id')
        primary_key_value = data.pop('_primary_key_value')
        
        db_config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'WWWwww123!',
            'database': database,
            'charset': 'utf8mb4'
        }
        
        try:
            with get_db_connection_context() as connection:
                cursor = connection.cursor()
                set_clauses = ', '.join([f"{col} = %s" for col in data.keys()])
                sql = f"UPDATE {table_name} SET {set_clauses} WHERE {primary_key} = %s"
                values = list(data.values()) + [primary_key_value]
                
                cursor.execute(sql, values)
                connection.commit()
                affected_rows = cursor.rowcount
            
            connection.close()
            
            return {
                'success': True,
                'affected_rows': affected_rows,
                'message': '记录更新成功'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'更新失败: {str(e)}'
            }
    
    def _execute_delete(self, table_name: str, record_id: str, primary_key: str, database: str) -> Dict:
        """执行删除操作"""
        db_config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'WWWwww123!',
            'database': database,
            'charset': 'utf8mb4'
        }
        
        try:
            with get_db_connection_context() as connection:
                cursor = connection.cursor()
                sql = f"DELETE FROM {table_name} WHERE {primary_key} = %s"
                cursor.execute(sql, [record_id])
                connection.commit()
                affected_rows = cursor.rowcount
            
            return {
                'success': True,
                'affected_rows': affected_rows,
                'message': '记录删除成功'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'删除失败: {str(e)}'
            }

    def _execute_batch_delete(self, table_name: str, record_ids: List[str], primary_key: str, database: str) -> Dict:
        """执行批量删除操作"""
        db_config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'WWWwww123!',
            'database': database,
            'charset': 'utf8mb4'
        }

        try:
            deleted_count = 0

            with get_db_connection_context() as connection:
                cursor = connection.cursor()
                # 使用IN子句进行批量删除
                placeholders = ', '.join(['%s'] * len(record_ids))
                sql = f"DELETE FROM {table_name} WHERE {primary_key} IN ({placeholders})"

                cursor.execute(sql, record_ids)
                connection.commit()
                deleted_count = cursor.rowcount

            connection.close()

            return {
                'success': True,
                'deleted_count': deleted_count,
                'message': f'成功删除 {deleted_count} 条记录'
            }

        except Exception as e:
            return {
                'success': False,
                'error': f'批量删除失败: {str(e)}'
            }
    
    def get_supported_tables(self) -> Dict:
        """获取支持的表列表"""
        try:
            tables = self.field_manager.get_supported_tables()
            return {
                'success': True,
                'tables': tables,
                'data_source': 'MySQL',
                'discovery_method': 'automatic'
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def validate_field_mapping(self, table_name: str) -> Dict:
        """验证字段映射"""
        return self.field_manager.validate_field_mapping(table_name)
    
    def override_table_config(self, table_name: str, config: Dict) -> Dict:
        """覆盖表配置"""
        try:
            self.field_manager.override_table_config(table_name, config)
            return {
                'success': True,
                'message': f'已覆盖表配置: {table_name}'
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def clear_cache(self):
        """清理缓存"""
        self.field_manager.clear_table_cache()
        self._cache.clear()
        logger.info("🧹 已清理所有缓存")

    def _validate_create_data(self, data: Dict, table_info: Dict) -> Dict:
        """验证创建数据"""
        required_fields = table_info.get('required_fields', [])
        fields = table_info.get('fields', [])

        # 检查必填字段
        missing_fields = []
        for field in required_fields:
            if field not in data or not data[field]:
                missing_fields.append(field)

        if missing_fields:
            return {
                'valid': False,
                'error': f'缺少必填字段: {", ".join(missing_fields)}'
            }

        # 检查字段是否存在于表中
        invalid_fields = []
        for field in data.keys():
            if field not in fields:
                invalid_fields.append(field)

        if invalid_fields:
            return {
                'valid': False,
                'error': f'无效字段: {", ".join(invalid_fields)}'
            }

        return {'valid': True}

    def _validate_update_data(self, data: Dict, table_info: Dict) -> Dict:
        """验证更新数据"""
        fields = table_info.get('fields', [])
        primary_key = table_info.get('primary_key', 'id')

        # 检查主键
        if primary_key not in data:
            return {
                'valid': False,
                'error': f'缺少主键字段: {primary_key}'
            }

        # 检查字段是否存在于表中
        invalid_fields = []
        for field in data.keys():
            if field not in fields:
                invalid_fields.append(field)

        if invalid_fields:
            return {
                'valid': False,
                'error': f'无效字段: {", ".join(invalid_fields)}'
            }

        return {'valid': True}

# 全局实例
enhanced_manager = None

def get_enhanced_manager():
    """获取增强版数据源管理器单例"""
    global enhanced_manager
    if enhanced_manager is None:
        enhanced_manager = EnhancedDataSourceManager()
    return enhanced_manager

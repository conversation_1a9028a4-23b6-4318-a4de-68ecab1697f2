#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
外部配置文件读取器
支持从exe同目录下的config.ini文件读取数据库配置
解决打包后无法修改数据库连接的问题
"""

import os
import sys
import configparser
import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class ExternalConfigReader:
    """外部配置文件读取器"""
    
    def __init__(self, config_file: str = 'config.ini'):
        """
        初始化配置读取器
        
        Args:
            config_file: 配置文件名，默认为config.ini
        """
        self.config_file = config_file
        self.config_path = self._get_config_path()
        self.config = configparser.ConfigParser()
        self._load_config()
    
    def _get_config_path(self) -> str:
        """获取配置文件路径"""
        # 如果是打包后的exe文件，从exe同目录读取
        if getattr(sys, 'frozen', False):
            # PyInstaller打包后的环境
            exe_dir = os.path.dirname(sys.executable)
            config_path = os.path.join(exe_dir, self.config_file)
        else:
            # 开发环境，从项目根目录读取
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            config_path = os.path.join(project_root, self.config_file)
        
        logger.info(f"配置文件路径: {config_path}")
        return config_path
    
    def _load_config(self) -> None:
        """加载配置文件"""
        try:
            if os.path.exists(self.config_path):
                # 尝试多种编码方式读取配置文件
                encodings = ['utf-8-sig', 'utf-8', 'ascii', 'gbk']
                config_loaded = False
                
                for encoding in encodings:
                    try:
                        self.config.read(self.config_path, encoding=encoding)
                        logger.info(f"✅ 成功加载外部配置文件: {self.config_path} (编码: {encoding})")
                        config_loaded = True
                        break
                    except Exception as enc_error:
                        logger.debug(f"使用{encoding}编码读取配置文件失败: {enc_error}")
                        continue
                
                if not config_loaded:
                    logger.error(f"❌ 所有编码方式都无法读取配置文件: {self.config_path}")
                    logger.info("将使用默认配置")
                else:
                    # 验证配置文件是否有必要的section
                    if not self.config.has_section('DATABASE'):
                        logger.warning("⚠️ 配置文件缺少[DATABASE]部分，将使用默认数据库配置")
                    else:
                        logger.info(f"✅ 配置文件验证成功，包含DATABASE部分")
            else:
                logger.warning(f"⚠️ 外部配置文件不存在: {self.config_path}")
                logger.info("将使用默认配置")
        except Exception as e:
            logger.error(f"❌ 加载配置文件失败: {e}")
            logger.info("将使用默认配置")
    
    def get_database_config(self) -> Dict[str, Any]:
        """获取数据库配置"""
        try:
            if self.config.has_section('DATABASE'):
                db_config = {
                    'host': self.config.get('DATABASE', 'host', fallback='localhost'),
                    'port': self.config.getint('DATABASE', 'port', fallback=3306),
                    'user': self.config.get('DATABASE', 'username', fallback='root'),  # 修复：使用'username'匹配config.ini中的字段名
                    'password': self.config.get('DATABASE', 'password', fallback='WWWwww123!'),
                    'database': self.config.get('DATABASE', 'database', fallback='aps'),
                    'charset': self.config.get('DATABASE', 'charset', fallback='utf8mb4')
                }
                logger.info(f"✅ 从外部配置文件读取数据库配置: {db_config['host']}:{db_config['port']}/{db_config['database']}")
                return db_config
        except Exception as e:
            logger.error(f"❌ 读取数据库配置失败: {e}")
        
        # 返回默认配置 - 从config.ini读取而不是硬编码
        logger.warning("⚠️ 无法从配置文件读取数据库配置，请检查config.ini文件是否存在且格式正确")
        default_config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': 'WWWwww123!',
            'database': 'aps',
            'charset': 'utf8mb4'
        }
        logger.info("使用默认数据库配置")
        return default_config
    
    def get_application_config(self) -> Dict[str, Any]:
        """获取应用配置"""
        try:
            if self.config.has_section('APPLICATION'):
                app_config = {
                    'debug': self.config.getboolean('APPLICATION', 'debug', fallback=False),
                    'host': self.config.get('APPLICATION', 'host', fallback='0.0.0.0'),
                    'port': self.config.getint('APPLICATION', 'port', fallback=5000),
                    'secret_key': self.config.get('APPLICATION', 'secret_key', fallback='dev-secret-key')
                }
                logger.info(f"✅ 从外部配置文件读取应用配置: {app_config['host']}:{app_config['port']}")
                return app_config
        except Exception as e:
            logger.error(f"❌ 读取应用配置失败: {e}")
        
        # 返回默认配置
        default_config = {
            'debug': False,
            'host': '0.0.0.0',
            'port': 5000,
            'secret_key': 'dev-secret-key'
        }
        logger.info("使用默认应用配置")
        return default_config
    
    def get_logging_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        try:
            if self.config.has_section('LOGGING'):
                log_config = {
                    'level': self.config.get('LOGGING', 'level', fallback='INFO'),
                    'file': self.config.get('LOGGING', 'file', fallback='logs/app.log'),
                    'max_size': self.config.get('LOGGING', 'max_size', fallback='10MB'),
                    'backup_count': self.config.getint('LOGGING', 'backup_count', fallback=5)
                }
                logger.info(f"✅ 从外部配置文件读取日志配置")
                return log_config
        except Exception as e:
            logger.error(f"❌ 读取日志配置失败: {e}")
        
        # 返回默认配置
        default_config = {
            'level': 'INFO',
            'file': 'logs/app.log',
            'max_size': '10MB',
            'backup_count': 5
        }
        return default_config
    
    def update_config(self, section: str, key: str, value: str) -> bool:
        """更新配置文件"""
        try:
            if not self.config.has_section(section):
                self.config.add_section(section)
            
            self.config.set(section, key, str(value))
            
            with open(self.config_path, 'w', encoding='utf-8') as f:
                self.config.write(f)
            
            logger.info(f"✅ 配置已更新: [{section}] {key} = {value}")
            return True
        except Exception as e:
            logger.error(f"❌ 更新配置失败: {e}")
            return False
    
    def config_exists(self) -> bool:
        """检查配置文件是否存在"""
        return os.path.exists(self.config_path)
    
    def get_config_path(self) -> str:
        """获取配置文件路径"""
        return self.config_path

# 全局配置读取器实例
_config_reader = None

def get_config_reader() -> ExternalConfigReader:
    """获取全局配置读取器实例"""
    global _config_reader
    if _config_reader is None:
        _config_reader = ExternalConfigReader()
    return _config_reader

def get_database_config() -> Dict[str, Any]:
    """获取数据库配置的便捷函数"""
    return get_config_reader().get_database_config()

def get_application_config() -> Dict[str, Any]:
    """获取应用配置的便捷函数"""
    return get_config_reader().get_application_config()

def get_logging_config() -> Dict[str, Any]:
    """获取日志配置的便捷函数"""
    return get_config_reader().get_logging_config() 
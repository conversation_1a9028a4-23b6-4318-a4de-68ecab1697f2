"""
STAGE字段映射配置模型
支持工序字段的精确匹配、模糊匹配、别名映射等多种映射类型
"""

from sqlalchemy import Column, Integer, String, Boolean, Text, DateTime, Enum
from sqlalchemy.sql import func
from app import db
import logging
from typing import List, Dict, Optional, Any

logger = logging.getLogger(__name__)

class StageMappingConfig(db.Model):
    """STAGE字段映射配置表"""
    __tablename__ = 'stage_mapping_config'
    
    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True)
    
    # 映射配置
    source_stage = Column(String(50), nullable=False, comment='源工序名称')
    target_stage = Column(String(50), nullable=False, comment='目标工序名称')
    mapping_type = Column(Enum('exact', 'fuzzy', 'alias'), default='exact', comment='映射类型')
    priority = Column(Integer, default=0, comment='优先级')
    is_active = Column(Boolean, default=True, comment='是否启用')
    description = Column(Text, comment='映射说明')
    
    # 操作记录
    created_by = Column(String(50), comment='创建人')
    updated_by = Column(String(50), comment='更新人')
    created_at = Column(DateTime, default=func.now(), comment='创建时间')
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')
    
    def __repr__(self):
        return f'<StageMappingConfig {self.source_stage} -> {self.target_stage}>'
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'source_stage': self.source_stage,
            'target_stage': self.target_stage,
            'mapping_type': self.mapping_type,
            'priority': self.priority,
            'is_active': self.is_active,
            'description': self.description,
            'created_by': self.created_by,
            'updated_by': self.updated_by,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def get_all_mappings(cls, active_only: bool = True) -> List['StageMappingConfig']:
        """获取所有映射配置"""
        try:
            query = cls.query
            if active_only:
                query = query.filter(cls.is_active == True)
            return query.order_by(cls.priority.desc(), cls.created_at.desc()).all()
        except Exception as e:
            logger.error(f"获取映射配置失败: {e}")
            return []
    
    @classmethod
    def get_mapping_by_source(cls, source_stage: str, active_only: bool = True) -> List['StageMappingConfig']:
        """根据源工序获取映射配置"""
        try:
            query = cls.query.filter(cls.source_stage == source_stage)
            if active_only:
                query = query.filter(cls.is_active == True)
            return query.order_by(cls.priority.desc()).all()
        except Exception as e:
            logger.error(f"根据源工序获取映射配置失败: {e}")
            return []
    
    @classmethod
    def find_target_stage(cls, source_stage: str) -> Optional[str]:
        """根据源工序查找目标工序"""
        try:
            # 首先尝试精确匹配
            exact_mapping = cls.query.filter(
                cls.source_stage == source_stage,
                cls.mapping_type == 'exact',
                cls.is_active == True
            ).order_by(cls.priority.desc()).first()
            
            if exact_mapping:
                return exact_mapping.target_stage
            
            # 如果没有精确匹配，尝试模糊匹配
            fuzzy_mappings = cls.query.filter(
                cls.mapping_type == 'fuzzy',
                cls.is_active == True
            ).order_by(cls.priority.desc()).all()
            
            for mapping in fuzzy_mappings:
                if mapping.source_stage.lower() in source_stage.lower() or \
                   source_stage.lower() in mapping.source_stage.lower():
                    return mapping.target_stage
            
            # 如果没有找到任何匹配，返回原始值
            return source_stage
            
        except Exception as e:
            logger.error(f"查找目标工序失败: {e}")
            return source_stage
    
    @classmethod
    def create_mapping(cls, source_stage: str, target_stage: str, 
                      mapping_type: str = 'exact', priority: int = 0,
                      description: str = None, created_by: str = None) -> Optional['StageMappingConfig']:
        """创建映射配置"""
        try:
            # 检查是否已存在相同映射
            existing = cls.query.filter(
                cls.source_stage == source_stage,
                cls.target_stage == target_stage
            ).first()
            
            if existing:
                logger.warning(f"映射配置已存在: {source_stage} -> {target_stage}")
                return None
            
            mapping = cls(
                source_stage=source_stage,
                target_stage=target_stage,
                mapping_type=mapping_type,
                priority=priority,
                description=description,
                created_by=created_by
            )
            
            db.session.add(mapping)
            db.session.commit()
            
            logger.info(f"创建映射配置成功: {source_stage} -> {target_stage}")
            return mapping
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"创建映射配置失败: {e}")
            return None
    
    def update_mapping(self, **kwargs) -> bool:
        """更新映射配置"""
        try:
            for key, value in kwargs.items():
                if hasattr(self, key):
                    setattr(self, key, value)
            
            db.session.commit()
            logger.info(f"更新映射配置成功: {self.source_stage} -> {self.target_stage}")
            return True
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"更新映射配置失败: {e}")
            return False
    
    def delete_mapping(self) -> bool:
        """删除映射配置"""
        try:
            db.session.delete(self)
            db.session.commit()
            
            logger.info(f"删除映射配置成功: {self.source_stage} -> {self.target_stage}")
            return True
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"删除映射配置失败: {e}")
            return False
    
    @classmethod
    def batch_create_default_mappings(cls, created_by: str = 'system') -> int:
        """批量创建默认映射配置"""
        try:
            # 常见的工序映射配置
            default_mappings = [
                ('FT', 'Final Test', 'exact', 100, '终测工序'),
                ('CP', 'Circuit Probe', 'exact', 100, '电路探针测试'),
                ('ASSEMBLY', 'Assembly', 'exact', 90, '组装工序'),
                ('PACKAGE', 'Package', 'exact', 90, '封装工序'),
                ('WAFER_TEST', 'Wafer Test', 'exact', 80, '晶圆测试'),
                ('BURN_IN', 'Burn In', 'exact', 70, '老化测试'),
                ('VISUAL', 'Visual Inspection', 'exact', 60, '外观检查'),
                ('TAPE_REEL', 'Tape and Reel', 'exact', 50, '编带装盘'),
            ]
            
            created_count = 0
            for source, target, mapping_type, priority, description in default_mappings:
                existing = cls.query.filter(
                    cls.source_stage == source,
                    cls.target_stage == target
                ).first()
                
                if not existing:
                    mapping = cls(
                        source_stage=source,
                        target_stage=target,
                        mapping_type=mapping_type,
                        priority=priority,
                        description=description,
                        created_by=created_by
                    )
                    db.session.add(mapping)
                    created_count += 1
            
            db.session.commit()
            logger.info(f"批量创建默认映射配置成功，共创建 {created_count} 条")
            return created_count
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"批量创建默认映射配置失败: {e}")
            return 0
    
    @classmethod
    def get_mapping_statistics(cls) -> Dict[str, int]:
        """获取映射配置统计信息"""
        try:
            stats = {
                'total_mappings': cls.query.count(),
                'active_mappings': cls.query.filter(cls.is_active == True).count(),
                'exact_mappings': cls.query.filter(cls.mapping_type == 'exact').count(),
                'fuzzy_mappings': cls.query.filter(cls.mapping_type == 'fuzzy').count(),
                'alias_mappings': cls.query.filter(cls.mapping_type == 'alias').count(),
            }
            return stats
            
        except Exception as e:
            logger.error(f"获取映射配置统计信息失败: {e}")
            return {} 